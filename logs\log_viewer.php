<?php
session_start();
require_once dirname(__FILE__) . '/../config/config.php';

// Verifica che l'utente sia admin
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
    header('Location: ' . BASE_URL);
    exit;
}

// Funzione per leggere i log con paginazione e filtri avanzati
function getLogs($options = []) {
    $defaults = [
        'page' => 1,
        'per_page' => 50,
        'severity' => null,
        'user' => null,
        'date_from' => null,
        'date_to' => null,
        'search' => null,
        'file' => null
    ];
    $options = array_merge($defaults, $options);
    
    $logFile = dirname(__FILE__) . '/errors.log';
    $logs = [];
    $total = 0;
    
    if (!file_exists($logFile)) {
        return ['logs' => [], 'total' => 0];
    }
    
    $lines = file($logFile);
    $lines = array_reverse($lines);
    
    foreach ($lines as $line) {
        if (empty(trim($line))) continue;
        
        preg_match('/\[(.*?)\]\[(.*?)\]\[(.*?)\]\[(.*?)\] (.*)/', $line, $matches);
        if (count($matches) >= 6) {
            $log = [
                'timestamp' => $matches[1],
                'severity' => $matches[2],
                'user' => $matches[3],
                'file' => $matches[4],
                'message' => $matches[5]
            ];
            
            // Applica filtri
            if ($options['severity'] && !str_contains($log['severity'], $options['severity'])) {
                continue;
            }
            if ($options['user'] && !str_contains($log['user'], $options['user'])) {
                continue;
            }
            if ($options['file'] && !str_contains($log['file'], $options['file'])) {
                continue;
            }
            if ($options['date_from']) {
                $logDate = strtotime($log['timestamp']);
                $fromDate = strtotime($options['date_from']);
                if ($logDate < $fromDate) continue;
            }
            if ($options['date_to']) {
                $logDate = strtotime($log['timestamp']);
                $toDate = strtotime($options['date_to'] . ' 23:59:59');
                if ($logDate > $toDate) continue;
            }
            if ($options['search'] && !str_contains(strtolower($log['message']), strtolower($options['search']))) {
                continue;
            }
            
            $total++;
            
            // Applica paginazione
            $start = ($options['page'] - 1) * $options['per_page'];
            if ($total > $start && count($logs) < $options['per_page']) {
                $logs[] = $log;
            }
        }
    }
    
    return [
        'logs' => $logs,
        'total' => $total
    ];
}

// Funzione per ottenere statistiche
function getLogStats() {
    $logFile = dirname(__FILE__) . '/errors.log';
    if (!file_exists($logFile)) {
        return [
            'total' => 0,
            'by_severity' => [],
            'by_user' => [],
            'by_date' => []
        ];
    }
    
    $stats = [
        'total' => 0,
        'by_severity' => [],
        'by_user' => [],
        'by_date' => []
    ];
    
    $lines = file($logFile);
    foreach ($lines as $line) {
        if (empty(trim($line))) continue;
        
        preg_match('/\[(.*?)\]\[(.*?)\]\[(.*?)\]\[(.*?)\] (.*)/', $line, $matches);
        if (count($matches) >= 6) {
            $stats['total']++;
            
            // Statistiche per severità
            $severity = trim($matches[2]);
            $stats['by_severity'][$severity] = ($stats['by_severity'][$severity] ?? 0) + 1;
            
            // Statistiche per utente
            $user = trim($matches[3]);
            $stats['by_user'][$user] = ($stats['by_user'][$user] ?? 0) + 1;
            
            // Statistiche per data
            $date = date('Y-m-d', strtotime($matches[1]));
            $stats['by_date'][$date] = ($stats['by_date'][$date] ?? 0) + 1;
        }
    }
    
    // Ordina le statistiche
    arsort($stats['by_severity']);
    arsort($stats['by_user']);
    ksort($stats['by_date']);
    
    return $stats;
}

// Funzione per ripulire il file di log
function clearLog() {
    $logFile = dirname(__FILE__) . '/errors.log';
    if (file_exists($logFile)) {
        // Crea un backup prima di cancellare
        $backupFile = dirname(__FILE__) . '/backups/errors_' . date('Y-m-d_H-i-s') . '.log';
        if (!is_dir(dirname(__FILE__) . '/backups')) {
            mkdir(dirname(__FILE__) . '/backups', 0777, true);
        }
        copy($logFile, $backupFile);
        // Azzera il file
        file_put_contents($logFile, '');
        return ['success' => true, 'message' => 'Log azzerato con successo. Backup creato: ' . basename($backupFile)];
    }
    return ['success' => false, 'message' => 'File di log non trovato'];
}

// Funzione per raggruppare i log per sessione utente
function getSessionLogs() {
    $logFile = dirname(__FILE__) . '/errors.log';
    if (!file_exists($logFile)) {
        return [];
    }

    $sessions = [];
    $currentSession = null;
    $lines = file($logFile);
    
    foreach ($lines as $line) {
        if (empty(trim($line))) continue;
        
        preg_match('/\[(.*?)\]\[(.*?)\]\[(.*?)\]\[(.*?)\] (.*)/', $line, $matches);
        if (count($matches) >= 6) {
            $timestamp = $matches[1];
            $severity = $matches[2];
            $user = $matches[3];
            $file = $matches[4];
            $message = $matches[5];
            
            // Cerca riferimenti alla sessione nel messaggio
            if (strpos($message, 'Session started') !== false || 
                strpos($message, 'Login successful') !== false) {
                $currentSession = [
                    'start_time' => $timestamp,
                    'user' => $user,
                    'events' => []
                ];
                $sessions[] = &$currentSession;
            }
            
            if ($currentSession) {
                $currentSession['events'][] = [
                    'timestamp' => $timestamp,
                    'severity' => $severity,
                    'message' => $message,
                    'file' => $file
                ];
                
                if (strpos($message, 'Logout') !== false || 
                    strpos($message, 'Session destroyed') !== false) {
                    $currentSession['end_time'] = $timestamp;
                    $currentSession = null;
                }
            }
        }
    }
    
    return $sessions;
}

// Gestione delle richieste AJAX
if (isset($_GET['action'])) {
    header('Content-Type: application/json');
    
    switch ($_GET['action']) {
        case 'get_logs':
            $options = [
                'page' => $_GET['page'] ?? 1,
                'severity' => $_GET['severity'] ?? null,
                'user' => $_GET['user'] ?? null,
                'date_from' => $_GET['date_from'] ?? null,
                'date_to' => $_GET['date_to'] ?? null,
                'search' => $_GET['search'] ?? null,
                'file' => $_GET['file'] ?? null
            ];
            echo json_encode(getLogs($options));
            break;
            
        case 'get_stats':
            echo json_encode(getLogStats());
            break;
            
        case 'clear_log':
            echo json_encode(clearLog());
            break;
            
        case 'get_session_logs':
            echo json_encode(getSessionLogs());
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Azione non valida']);
    }
    exit;
}
?>

<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visualizzatore Log - Studio Tecnico</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .severity-badge {
            width: 80px;
            text-align: center;
        }
        .log-table {
            font-size: 0.9rem;
        }
        .stats-card {
            height: 100%;
        }
        .filter-section {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.25rem;
            margin-bottom: 1rem;
        }
        .session-card {
            margin-bottom: 1rem;
        }
        .session-events {
            max-height: 300px;
            overflow-y: auto;
        }
        .nav-tabs {
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-3">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">Visualizzatore Log</h1>
            <div>
                <button onclick="clearLogs()" class="btn btn-danger me-2">
                    <i class="bi bi-trash"></i> Azzera Log
                </button>
                <button onclick="window.close()" class="btn btn-secondary">
                    <i class="bi bi-x-lg"></i> Chiudi
                </button>
            </div>
        </div>

        <!-- Tab Navigation -->
        <ul class="nav nav-tabs" role="tablist">
            <li class="nav-item">
                <a class="nav-link active" data-bs-toggle="tab" href="#logTab">Log Completo</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="tab" href="#sessionsTab">Sessioni Utente</a>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content">
            <!-- Tab Log Completo -->
            <div class="tab-pane fade show active" id="logTab">
                <!-- Filtri -->
                <div class="filter-section">
                    <form id="filterForm" class="row g-3">
                        <div class="col-md-2">
                            <label class="form-label">Severità</label>
                            <select class="form-select" name="severity">
                                <option value="">Tutti</option>
                                <option value="FATAL">Fatal</option>
                                <option value="ERROR">Error</option>
                                <option value="WARN">Warning</option>
                                <option value="NOTE">Notice</option>
                                <option value="INFO">Info</option>
                                <option value="QUERY">Query</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Data Inizio</label>
                            <input type="date" class="form-control" name="date_from">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Data Fine</label>
                            <input type="date" class="form-control" name="date_to">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Utente</label>
                            <input type="text" class="form-control" name="user" placeholder="Filtra per utente">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">File</label>
                            <input type="text" class="form-control" name="file" placeholder="Filtra per file">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Cerca</label>
                            <input type="text" class="form-control" name="search" placeholder="Cerca nel messaggio">
                        </div>
                    </form>
                </div>

                <!-- Statistiche -->
                <div class="row mb-4" id="statsContainer">
                    <!-- Statistiche verranno caricate qui -->
                </div>

                <!-- Tabella Log -->
                <div class="table-responsive">
                    <table class="table table-sm table-hover log-table">
                        <thead>
                            <tr>
                                <th>Data/Ora</th>
                                <th>Severità</th>
                                <th>Utente</th>
                                <th>File</th>
                                <th>Messaggio</th>
                            </tr>
                        </thead>
                        <tbody id="logTableBody">
                            <!-- I log verranno caricati qui -->
                        </tbody>
                    </table>
                </div>

                <!-- Paginazione -->
                <nav>
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- La paginazione verrà caricata qui -->
                    </ul>
                </nav>
            </div>

            <!-- Tab Sessioni Utente -->
            <div class="tab-pane fade" id="sessionsTab">
                <div id="sessionsContainer">
                    <!-- Le sessioni verranno caricate qui -->
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentPage = 1;
        let totalPages = 1;
        
        // Funzione per caricare i log
        async function loadLogs(page = 1) {
            const form = document.getElementById('filterForm');
            const formData = new FormData(form);
            formData.append('page', page);
            
            const queryString = new URLSearchParams(formData).toString();
            
            try {
                const response = await fetch(`?action=get_logs&${queryString}`);
                const data = await response.json();
                
                const tbody = document.getElementById('logTableBody');
                tbody.innerHTML = '';
                
                data.logs.forEach(log => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${log.timestamp}</td>
                        <td><span class="badge severity-badge bg-${getSeverityClass(log.severity)}">${log.severity}</span></td>
                        <td>${log.user}</td>
                        <td>${log.file}</td>
                        <td>${log.message}</td>
                    `;
                    tbody.appendChild(row);
                });
                
                updatePagination(data.total);
                
            } catch (error) {
                console.error('Errore nel caricamento dei log:', error);
            }
        }

        // Funzione per caricare le sessioni
        async function loadSessions() {
            try {
                const response = await fetch('?action=get_session_logs');
                const sessions = await response.json();
                
                const container = document.getElementById('sessionsContainer');
                container.innerHTML = '';
                
                sessions.forEach((session, index) => {
                    const card = document.createElement('div');
                    card.className = 'card session-card';
                    card.innerHTML = `
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="bi bi-person-circle"></i> ${session.user}
                                </h5>
                                <span class="text-muted">
                                    <i class="bi bi-clock"></i> ${session.start_time}
                                    ${session.end_time ? ' → ' + session.end_time : ' (Sessione attiva)'}
                                </span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="session-events">
                                <div class="list-group list-group-flush">
                                    ${session.events.map(event => `
                                        <div class="list-group-item">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span class="badge bg-${getSeverityClass(event.severity)}">${event.severity}</span>
                                                <small class="text-muted">${event.timestamp}</small>
                                            </div>
                                            <p class="mb-1">${event.message}</p>
                                            <small class="text-muted">${event.file}</small>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                    `;
                    container.appendChild(card);
                });
                
                if (sessions.length === 0) {
                    container.innerHTML = '<div class="alert alert-info">Nessuna sessione trovata</div>';
                }
                
            } catch (error) {
                console.error('Errore nel caricamento delle sessioni:', error);
            }
        }
        
        // Funzione per azzerare i log
        async function clearLogs() {
            if (!confirm('Sei sicuro di voler azzerare il file di log? Verrà creato un backup prima di procedere.')) {
                return;
            }
            
            try {
                const response = await fetch('?action=clear_log');
                const result = await response.json();
                
                if (result.success) {
                    alert(result.message);
                    loadLogs(1);
                    loadStats();
                    loadSessions();
                } else {
                    alert('Errore: ' + result.message);
                }
            } catch (error) {
                console.error('Errore durante l\'azzeramento dei log:', error);
                alert('Errore durante l\'azzeramento dei log');
            }
        }
        
        // Funzione per caricare le statistiche
        async function loadStats() {
            try {
                const response = await fetch('?action=get_stats');
                const stats = await response.json();
                
                const container = document.getElementById('statsContainer');
                container.innerHTML = `
                    <div class="col-md-3">
                        <div class="card stats-card">
                            <div class="card-body">
                                <h5 class="card-title">Totale Log</h5>
                                <p class="card-text display-6">${stats.total}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card">
                            <div class="card-body">
                                <h5 class="card-title">Per Severità</h5>
                                <ul class="list-unstyled">
                                    ${Object.entries(stats.by_severity)
                                        .map(([severity, count]) => 
                                            `<li>${severity}: ${count}</li>`
                                        ).join('')}
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card">
                            <div class="card-body">
                                <h5 class="card-title">Per Utente</h5>
                                <ul class="list-unstyled">
                                    ${Object.entries(stats.by_user)
                                        .map(([user, count]) => 
                                            `<li>${user}: ${count}</li>`
                                        ).join('')}
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card">
                            <div class="card-body">
                                <h5 class="card-title">Ultimi Giorni</h5>
                                <ul class="list-unstyled">
                                    ${Object.entries(stats.by_date)
                                        .slice(-5)
                                        .map(([date, count]) => 
                                            `<li>${date}: ${count}</li>`
                                        ).join('')}
                                </ul>
                            </div>
                        </div>
                    </div>
                `;
            } catch (error) {
                console.error('Errore nel caricamento delle statistiche:', error);
            }
        }
        
        // Funzione per aggiornare la paginazione
        function updatePagination(total) {
            const perPage = 50;
            totalPages = Math.ceil(total / perPage);
            
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';
            
            // Pulsante precedente
            pagination.innerHTML += `
                <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">Precedente</a>
                </li>
            `;
            
            // Numeri pagina
            for (let i = 1; i <= totalPages; i++) {
                if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                    pagination.innerHTML += `
                        <li class="page-item ${i === currentPage ? 'active' : ''}">
                            <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                        </li>
                    `;
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    pagination.innerHTML += `
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    `;
                }
            }
            
            // Pulsante successivo
            pagination.innerHTML += `
                <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">Successivo</a>
                </li>
            `;
        }
        
        // Funzione per cambiare pagina
        function changePage(page) {
            if (page >= 1 && page <= totalPages) {
                currentPage = page;
                loadLogs(page);
            }
            return false;
        }
        
        // Funzione per ottenere la classe del badge in base alla severità
        function getSeverityClass(severity) {
            return {
                '🔴 FATAL': 'danger',
                '🟠 ERROR': 'warning',
                '🟡 WARN': 'warning',
                '🔵 NOTE': 'info',
                '⚪ INFO': 'secondary',
                '🟣 QUERY': 'primary'
            }[severity] || 'secondary';
        }
        
        // Inizializzazione
        document.addEventListener('DOMContentLoaded', function() {
            loadLogs();
            loadStats();
            loadSessions();
            
            // Gestione filtri
            const filterForm = document.getElementById('filterForm');
            filterForm.addEventListener('change', function() {
                currentPage = 1;
                loadLogs(1);
            });
            
            // Gestione cambio tab
            const tabs = document.querySelectorAll('a[data-bs-toggle="tab"]');
            tabs.forEach(tab => {
                tab.addEventListener('shown.bs.tab', function(e) {
                    if (e.target.getAttribute('href') === '#sessionsTab') {
                        loadSessions();
                    }
                });
            });
        });
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
