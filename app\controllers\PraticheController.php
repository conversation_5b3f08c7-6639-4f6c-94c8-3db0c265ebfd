<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Config\Database;
use PDO;
use PDOException;

class PraticheController extends Controller {
    private $db;

    public function __construct() {
        try {
            parent::__construct();
            $this->db = Database::getInstance();
            if (!$this->db instanceof PDO) {
                throw new \Exception("Connessione al database non valida");
            }
        } catch (\Exception $e) {
            error_log("Errore nell'inizializzazione del controller: " . $e->getMessage());
            throw $e;
        }
    }

    public function index() {
        try {
            // Log per debug
            error_log("Inizio recupero pratiche");
            
            $query = "
                SELECT p.*, pr.nome_progetto, 
                       CASE 
                           WHEN c.tipo_cliente = 'privato' THEN CONCAT(c.nome, ' ', c.cognome)
                           ELSE c.ragione_sociale 
                       END as cliente_nome,
                       COALESCE(p.tipo_documento, p.tipo_pratica) as tipo_documento
                FROM pratiche p 
                JOIN progetti pr ON p.progetto_id = pr.id 
                JOIN clienti c ON pr.cliente_id = c.id 
                ORDER BY p.data_apertura DESC
            ";
            
            error_log("Query SQL: " . $query);
            
            $stmt = $this->db->query($query);
            
            if ($stmt === false) {
                throw new PDOException("Errore nell'esecuzione della query");
            }
            
            $pratiche = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Log del numero di pratiche trovate
            error_log("Numero di pratiche trovate: " . count($pratiche));
            
            // Debug: log dei dettagli di ogni pratica
            foreach ($pratiche as $pratica) {
                error_log("Pratica trovata - ID: " . $pratica['id'] . 
                         ", Numero: " . $pratica['numero_pratica'] . 
                         ", Cliente: " . $pratica['cliente_nome'] . 
                         ", Progetto: " . $pratica['nome_progetto'] . 
                         ", Tipo: " . ($pratica['tipo_documento'] ?? 'non specificato') . 
                         ", Stato: " . ($pratica['stato'] ?? 'non specificato'));
            }
            
            // Debug: verifica se i dati vengono passati correttamente alla vista
            error_log("Debug controller - Dati passati alla vista: " . print_r(['pratiche' => $pratiche], true));
            
            return ['view' => 'pratiche/index', 'pratiche' => $pratiche];
        } catch (PDOException $e) {
            error_log("Errore nella pagina index delle pratiche: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            return ['view' => 'pratiche/index', 'data' => ['error' => 'Errore nel caricamento delle pratiche']];
        }
    }

    public function nuovo() {
        // Inizializza $viewData con 'progetti' come array vuoto di default.
        // Questo garantisce che 'progetti' sia sempre presente nei dati passati alla vista.
        $viewData = ['progetti' => []];

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                // Validazione dei dati
                $progetto_id = $_POST['progetto_id'] ?? null;
                $tipo_documento = $_POST['tipo_documento'] ?? null;
                $descrizione = $_POST['descrizione'] ?? null;
                $stato = $_POST['stato'] ?? 'aperta'; // Default se non fornito
                
                // Popola 'old' per ripopolare il form in caso di errore
                $viewData['old'] = $_POST;

                if (!$progetto_id || !$tipo_documento || !$descrizione) {
                    throw new \Exception("Tutti i campi obbligatori non sono stati compilati.");
                }

                $sql = "INSERT INTO pratiche (progetto_id, tipo_documento, descrizione, stato, data_apertura) 
                        VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)";
                $stmt = $this->db->prepare($sql);
                $result = $stmt->execute([$progetto_id, $tipo_documento, $descrizione, $stato]);

                if ($result) {
                    $_SESSION['success'] = 'Pratica creata con successo.';
                    $this->redirect('pratiche'); 
                    exit; 
                } else {
                    // Se $result è false, l'esecuzione della query è fallita ma non ha lanciato un'eccezione PDO
                    // (potrebbe dipendere dalla configurazione di ERRMODE di PDO)
                    throw new \PDOException("Errore durante il salvataggio della pratica nel database.");
                }

            } catch (\Exception $e) {
                error_log("Errore nella creazione della pratica: " . $e->getMessage());
                $viewData['error'] = $e->getMessage();
                
                // Anche in caso di errore POST, proviamo a caricare i progetti per il form
                try {
                    $stmt = $this->db->query("
                        SELECT p.id, p.nome_progetto, 
                               CASE 
                                   WHEN c.tipo_cliente = 'privato' THEN CONCAT(c.nome, ' ', c.cognome)
                                   ELSE c.ragione_sociale 
                               END as cliente_nome
                        FROM progetti p
                        JOIN clienti c ON p.cliente_id = c.id
                        ORDER BY p.nome_progetto
                    ");
                    // Sovrascrive $viewData['progetti'] (che era []) con i risultati, se presenti
                    $viewData['progetti'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
                } catch (\Exception $dbEx) {
                    error_log("Errore nel caricamento dei progetti dopo errore POST: " . $dbEx->getMessage());
                    // $viewData['progetti'] rimane [], che è corretto
                    $viewData['error'] .= " (Inoltre, si è verificato un errore nel ricaricare la lista dei progetti per il form.)";
                }
                
                $this->view('pratiche/nuovo', $viewData);
                return; // Termina l'esecuzione per non passare al blocco GET
            }
        } else { // GET request - mostra il form
            try {
                // Recupera la lista dei progetti per il select
                $stmt = $this->db->query("
                    SELECT p.id, p.nome_progetto, 
                           CASE 
                               WHEN c.tipo_cliente = 'privato' THEN CONCAT(c.nome, ' ', c.cognome)
                               ELSE c.ragione_sociale 
                           END as cliente_nome
                    FROM progetti p
                    JOIN clienti c ON p.cliente_id = c.id
                    ORDER BY p.nome_progetto
                ");
                // Sovrascrive $viewData['progetti'] (che era []) con i risultati
                $viewData['progetti'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            } catch (\Exception $e) {
                error_log("Errore nel caricamento dei progetti per il form: " . $e->getMessage());
                $viewData['error'] = 'Errore nel caricamento della lista dei progetti.';
                // $viewData['progetti'] rimane [], che è corretto
            }
            $this->view('pratiche/nuovo', $viewData);
        }
    }

    public function dettagli($params) {
        $id = isset($params[0]) ? intval($params[0]) : null;
        $isModal = isset($_GET['modal']) && $_GET['modal'] === 'true';
        
        if (!$id) {
            if ($isModal) {
                echo "Pratica non trovata";
                exit;
            }
            header('Location: ' . BASE_URL . 'pratiche?error=pratica_non_trovata');
            exit;
        }

        try {
            $query = "SELECT 
                        pr.*,
                        p.nome_progetto,
                        c.nome as cliente_nome,
                        c.cognome as cliente_cognome,
                        c.ragione_sociale as cliente_ragione_sociale,
                        c.tipo_cliente
                    FROM pratiche pr
                    JOIN progetti p ON pr.progetto_id = p.id
                    JOIN clienti c ON p.cliente_id = c.id
                    WHERE pr.id = :id";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute(['id' => $id]);
            $pratica = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$pratica) {
                if ($isModal) {
                    echo "Pratica non trovata";
                    exit;
                }
                header('Location: ' . BASE_URL . 'pratiche?error=pratica_non_trovata');
                exit;
            }

            // Se è una richiesta modal, carica una vista semplificata
            if ($isModal) {
                $this->view('pratiche/dettagli_modal', ['pratica' => $pratica]);
                exit;
            }

            $this->view('pratiche/dettagli', ['pratica' => $pratica]);

        } catch (PDOException $e) {
            error_log("Errore nel recupero dei dettagli della pratica: " . $e->getMessage());
            if ($isModal) {
                echo "Errore nel caricamento dei dettagli";
                exit;
            }
            header('Location: ' . BASE_URL . 'pratiche?error=errore_recupero_dati');
            exit;
        }
    }

    public function modifica($id) {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                // Validazione dei dati
                $tipo_documento = $_POST['tipo_documento'] ?? null;
                $descrizione = $_POST['descrizione'] ?? null;
                $stato = $_POST['stato'] ?? null;
                $numero_pratica = $_POST['numero_pratica'] ?? null;
                $progetto_id = $_POST['progetto_id'] ?? null;
                
                if (!$tipo_documento || !$descrizione || !$stato || !$numero_pratica || !$progetto_id) {
                    throw new \Exception("Tutti i campi sono obbligatori");
                }

                $sql = "UPDATE pratiche 
                        SET tipo_documento = ?, 
                            descrizione = ?, 
                            stato = ?,
                            numero_pratica = ? 
                        WHERE id = ? AND progetto_id = ?";
                $stmt = $this->db->prepare($sql);
                $result = $stmt->execute([$tipo_documento, $descrizione, $stato, $numero_pratica, $id, $progetto_id]);

                if ($result) {
                    $_SESSION['success'] = 'Pratica aggiornata con successo';
                    header('Location: ' . BASE_URL . 'pratiche');
                    exit;
                }
            } catch (\Exception $e) {
                error_log("Errore nell'aggiornamento della pratica: " . $e->getMessage());
                return [
                    'view' => 'pratiche/modifica',
                    'data' => [
                        'error' => $e->getMessage(),
                        'pratica' => $_POST
                    ]
                ];
            }
        }

        // GET request - mostra il form con i dati attuali
        try {
            // Recupera i dati della pratica
            $stmt = $this->db->prepare("
                SELECT p.*, pr.nome_progetto,
                       CASE 
                           WHEN c.tipo_cliente = 'privato' THEN CONCAT(c.nome, ' ', c.cognome)
                           ELSE c.ragione_sociale 
                       END as cliente_nome
                FROM pratiche p
                JOIN progetti pr ON p.progetto_id = pr.id
                JOIN clienti c ON pr.cliente_id = c.id
                WHERE p.id = ?
            ");
            $stmt->execute([$id]);
            $pratica = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$pratica) {
                throw new \Exception("Pratica non trovata");
            }

            return [
                'view' => 'pratiche/modifica',
                'data' => [
                    'pratica' => $pratica
                ]
            ];
        } catch (\Exception $e) {
            error_log("Errore nel caricamento della pratica: " . $e->getMessage());
            return [
                'view' => 'pratiche/modifica',
                'data' => ['error' => 'Errore nel caricamento della pratica']
            ];
        }
    }

    public function elimina($id) {
        try {
            $stmt = $this->db->prepare("DELETE FROM pratiche WHERE id = ?");
            $result = $stmt->execute([$id]);

            if ($result) {
                $_SESSION['success'] = 'Pratica eliminata con successo';
            } else {
                $_SESSION['error'] = 'Impossibile eliminare la pratica';
            }
        } catch (\Exception $e) {
            error_log("Errore nell'eliminazione della pratica: " . $e->getMessage());
            $_SESSION['error'] = 'Errore durante l\'eliminazione della pratica';
        }

        header('Location: ' . BASE_URL . 'pratiche');
        exit;
    }
}
