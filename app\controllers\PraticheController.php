<?php
// app/controllers/PraticheController.php - Controller per la gestione delle pratiche edilizie
namespace App\Controllers;

use App\Core\Controller;
use App\Config\Database;
use App\Models\PraticheModel;
use App\Models\ProgettiModel;
use App\Services\NotificationService;
use PDO;
use PDOException;

class PraticheController extends Controller {
    private PDO $db;
    private PraticheModel $praticheModel;
    private ProgettiModel $progettiModel;
    private NotificationService $notificationService;

    public function __construct() {
        try {
            parent::__construct();
            $this->db = Database::getInstance();
            if (!$this->db instanceof PDO) {
                throw new \Exception("Connessione al database non valida");
            }
            $this->praticheModel = new PraticheModel();
            $this->progettiModel = new ProgettiModel();
            $this->notificationService = new NotificationService();
        } catch (\Exception $e) {
            error_log("Errore nell'inizializzazione del PraticheController: " . $e->getMessage());
            throw $e;
        }
    }

    public function index() {
        try {
            // Recupera filtri dalla query string se presenti
            $filters = [];
            if (!empty($_GET['stato'])) {
                $filters['stato'] = $_GET['stato'];
            }
            if (!empty($_GET['progetto_id'])) {
                $filters['progetto_id'] = (int)$_GET['progetto_id'];
            }
            if (!empty($_GET['tipo_documento'])) {
                $filters['tipo_documento'] = $_GET['tipo_documento'];
            }
            if (!empty($_GET['scadenza_da'])) {
                $filters['scadenza_da'] = $_GET['scadenza_da'];
            }
            if (!empty($_GET['scadenza_a'])) {
                $filters['scadenza_a'] = $_GET['scadenza_a'];
            }

            // Recupera pratiche usando il Model
            $pratiche = $this->praticheModel->getAllPratiche($filters);

            // Recupera statistiche per la dashboard
            $statistiche = $this->praticheModel->getStatistichePratiche();

            // Recupera pratiche critiche (in scadenza entro 3 giorni)
            $pratiche_critiche = $this->praticheModel->getPraticheCritiche();

            error_log("PraticheController::index() - Pratiche trovate: " . count($pratiche));

            $this->view('pratiche/index', [
                'pratiche' => $pratiche,
                'statistiche' => $statistiche,
                'pratiche_critiche' => $pratiche_critiche,
                'filters' => $filters,
                'stati_disponibili' => $this->praticheModel->getStatiDisponibili()
            ]);

        } catch (\Exception $e) {
            error_log("Errore in PraticheController::index(): " . $e->getMessage());
            $this->view('pratiche/index', [
                'pratiche' => [],
                'statistiche' => [],
                'pratiche_critiche' => [],
                'filters' => [],
                'stati_disponibili' => [],
                'error' => 'Errore nel caricamento delle pratiche'
            ]);
        }
    }

    public function nuovo() {
        try {
            // Recupera progetti per il select usando il ProgettiModel
            $progetti = $this->progettiModel->getAllProgetti();

            if ($this->isPost()) {
                // Verifica CSRF token
                try {
                    $this->requireCSRF();
                } catch (\Exception $e) {
                    error_log("PraticheController::nuovo() - " . $e->getMessage());
                    $this->view('pratiche/nuovo', [
                        'progetti' => $progetti,
                        'stati_disponibili' => $this->praticheModel->getStatiDisponibili(),
                        'errori' => ['csrf' => 'Token di sicurezza non valido. Riprova.'],
                        'old' => $_POST ?? []
                    ]);
                    return;
                }

                // Validazione
                $errori = [];
                if (empty($this->getPost('progetto_id'))) $errori['progetto_id'] = "Il progetto è obbligatorio";
                if (empty($this->getPost('tipo_documento'))) $errori['tipo_documento'] = "Il tipo documento è obbligatorio";
                if (empty($this->getPost('numero_pratica'))) $errori['numero_pratica'] = "Il numero pratica è obbligatorio";

                if (empty($errori)) {
                    try {
                        // Prepara i dati per il Model
                        $data = [
                            'progetto_id' => $this->getPost('progetto_id'),
                            'tipo_documento' => $this->getPost('tipo_documento'),
                            'tipo_pratica' => $this->getPost('tipo_pratica'),
                            'numero_pratica' => $this->getPost('numero_pratica'),
                            'stato' => $this->getPost('stato') ?: 'in_attesa',
                            'data_scadenza' => $this->getPost('data_scadenza'),
                            'data_scadenza_integrazione' => $this->getPost('data_scadenza_integrazione'),
                            'ente_riferimento' => $this->getPost('ente_riferimento'),
                            'protocollo' => $this->getPost('protocollo'),
                            'data_protocollo' => $this->getPost('data_protocollo'),
                            'importo_diritti' => $this->getPost('importo_diritti'),
                            'note' => $this->getPost('note'),
                            'note_interne' => $this->getPost('note_interne'),
                            'documenti_richiesti' => $this->getPost('documenti_richiesti'),
                            'responsabile' => $this->getPost('responsabile')
                        ];

                        if ($this->praticheModel->insertPratica($data)) {
                            // Crea notifica per nuova pratica
                            $this->notificationService->createNotificaPratica(
                                $this->db->lastInsertId(),
                                'nuova_pratica',
                                $_SESSION['user_id'] ?? 1
                            );

                            $this->redirect('pratiche');
                        } else {
                            $errori[] = "Errore durante il salvataggio della pratica";
                        }
                    } catch (\Exception $e) {
                        error_log("Errore in PraticheController::nuovo(): " . $e->getMessage());
                        $errori[] = "Errore durante il salvataggio: " . $e->getMessage();
                    }
                }
            }

            $this->view('pratiche/nuovo', [
                'progetti' => $progetti,
                'stati_disponibili' => $this->praticheModel->getStatiDisponibili(),
                'errori' => $errori ?? [],
                'old' => $_POST ?? []
            ]);

        } catch (\Exception $e) {
            error_log("Errore in PraticheController::nuovo(): " . $e->getMessage());
            $this->view('pratiche/nuovo', [
                'progetti' => [],
                'stati_disponibili' => [],
                'errori' => ['Errore nel caricamento della pagina'],
                'old' => $_POST ?? []
            ]);
        }
    }

    public function dettagli($params) {
        $id = isset($params[0]) ? intval($params[0]) : null;
        $isModal = isset($_GET['modal']) && $_GET['modal'] === 'true';

        if (!$id) {
            if ($isModal) {
                echo "Pratica non trovata";
                exit;
            }
            header('Location: ' . BASE_URL . 'pratiche?error=pratica_non_trovata');
            exit;
        }

        try {
            // Recupera i dati della pratica usando il Model
            $pratica = $this->praticheModel->getPraticaById($id);

            if (!$pratica) {
                if ($isModal) {
                    echo "Pratica non trovata";
                    exit;
                }
                header('Location: ' . BASE_URL . 'pratiche?error=pratica_non_trovata');
                exit;
            }

            // Recupera transizioni disponibili per il workflow
            $transizioni_disponibili = $this->praticheModel->getAvailableTransitions($pratica['stato']);

            // Se è una richiesta modal, carica una vista semplificata
            if ($isModal) {
                $this->view('pratiche/dettagli_modal', [
                    'pratica' => $pratica,
                    'transizioni_disponibili' => $transizioni_disponibili
                ]);
                exit;
            }

            $this->view('pratiche/dettagli', [
                'pratica' => $pratica,
                'transizioni_disponibili' => $transizioni_disponibili,
                'stati_disponibili' => $this->praticheModel->getStatiDisponibili()
            ]);

        } catch (\Exception $e) {
            error_log("Errore in PraticheController::dettagli(): " . $e->getMessage());
            if ($isModal) {
                echo "Errore nel caricamento dei dettagli";
                exit;
            }
            header('Location: ' . BASE_URL . 'pratiche?error=errore_recupero_dati');
            exit;
        }
    }

    /**
     * Cambia stato di una pratica con workflow automatizzato
     */
    public function cambiaStato($params) {
        try {
            // Verifica CSRF token
            $this->requireCSRF();

            $id = isset($params[0]) ? (int)$params[0] : null;
            $nuovo_stato = $this->getPost('nuovo_stato');
            $user_id = $_SESSION['user_id'] ?? 1;

            if (!$id || !$nuovo_stato) {
                throw new \Exception('Parametri mancanti per il cambio stato');
            }

            // Verifica che la pratica esista
            $pratica = $this->praticheModel->getPraticaById($id);
            if (!$pratica) {
                throw new \Exception('Pratica non trovata');
            }

            // Verifica che la transizione sia consentita
            if (!$this->praticheModel->canTransition($pratica['stato'], $nuovo_stato)) {
                throw new \Exception("Transizione da '{$pratica['stato']}' a '{$nuovo_stato}' non consentita");
            }

            // Aggiorna lo stato
            if ($this->praticheModel->updateStatoPratica($id, $nuovo_stato, $user_id)) {
                // Crea notifica automatica per il cambio stato
                $this->notificationService->createNotificaPratica($id, 'stato_cambiato', $user_id);

                if ($this->isAjax()) {
                    $this->jsonResponse([
                        'success' => true,
                        'message' => "Stato aggiornato a: {$nuovo_stato}",
                        'nuovo_stato' => $nuovo_stato
                    ]);
                } else {
                    $this->redirect("pratiche/dettagli/{$id}?success=stato_aggiornato");
                }
            } else {
                throw new \Exception('Errore durante l\'aggiornamento dello stato');
            }

        } catch (\Exception $e) {
            error_log("Errore in PraticheController::cambiaStato(): " . $e->getMessage());

            if ($this->isAjax()) {
                $this->jsonResponse(['error' => $e->getMessage()], 400);
            } else {
                $id = $params[0] ?? '';
                $this->redirect("pratiche/dettagli/{$id}?error=" . urlencode($e->getMessage()));
            }
        }
    }

    /**
     * API per recuperare pratiche in scadenza (AJAX)
     */
    public function getScadenzeAjax() {
        try {
            $giorni = (int)($_GET['giorni'] ?? 7);
            $pratiche_scadenza = $this->praticheModel->getPraticheInScadenza($giorni);

            $this->jsonResponse([
                'success' => true,
                'pratiche' => array_map(function($p) {
                    return [
                        'id' => $p['id'],
                        'numero_pratica' => $p['numero_pratica'],
                        'nome_progetto' => $p['nome_progetto'],
                        'cliente_nome_completo' => $p['cliente_nome_completo'],
                        'data_scadenza' => $p['data_scadenza'],
                        'giorni_rimanenti' => $p['giorni_rimanenti'],
                        'stato' => $p['stato']
                    ];
                }, $pratiche_scadenza),
                'count' => count($pratiche_scadenza)
            ]);

        } catch (\Exception $e) {
            error_log("Errore in PraticheController::getScadenzeAjax(): " . $e->getMessage());
            $this->jsonResponse(['error' => 'Errore nel recupero delle scadenze'], 500);
        }
    }

    /**
     * Metodi di utilità
     */

    /**
     * Verifica se la richiesta è AJAX
     */
    private function isAjax(): bool {
        return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }

    /**
     * Risposta JSON per API
     */
    private function jsonResponse(array $data, int $statusCode = 200): void {
        http_response_code($statusCode);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * Metodo per recuperare pratiche per progetto (API)
     */
    public function getByProgetto($params) {
        try {
            $progetto_id = isset($params[0]) ? (int)$params[0] : null;

            if (!$progetto_id) {
                throw new \Exception('ID progetto mancante');
            }

            $pratiche = $this->praticheModel->getPraticheByProgettoId($progetto_id);

            $this->jsonResponse([
                'success' => true,
                'pratiche' => $pratiche,
                'count' => count($pratiche)
            ]);

        } catch (\Exception $e) {
            error_log("Errore in PraticheController::getByProgetto(): " . $e->getMessage());
            $this->jsonResponse(['error' => $e->getMessage()], 400);
        }
    }

    /**
     * Dashboard statistiche pratiche
     */
    public function statistiche() {
        try {
            $statistiche = $this->praticheModel->getStatistichePratiche();
            $pratiche_critiche = $this->praticheModel->getPraticheCritiche();
            $pratiche_in_scadenza = $this->praticheModel->getPraticheInScadenza(7);

            $this->view('pratiche/statistiche', [
                'statistiche' => $statistiche,
                'pratiche_critiche' => $pratiche_critiche,
                'pratiche_in_scadenza' => $pratiche_in_scadenza,
                'stati_disponibili' => $this->praticheModel->getStatiDisponibili()
            ]);

        } catch (\Exception $e) {
            error_log("Errore in PraticheController::statistiche(): " . $e->getMessage());
            $this->view('pratiche/statistiche', [
                'statistiche' => [],
                'pratiche_critiche' => [],
                'pratiche_in_scadenza' => [],
                'stati_disponibili' => [],
                'error' => 'Errore nel caricamento delle statistiche'
            ]);
        }
    }

    /**
     * Metodo per aggiornare data scadenza
     */
    public function updateScadenza($params) {
        try {
            // Verifica CSRF token
            $this->requireCSRF();

            $id = isset($params[0]) ? (int)$params[0] : null;
            $nuova_data = $this->getPost('nuova_data_scadenza');
            $tipo = $this->getPost('tipo_scadenza') ?: 'principale';

            if (!$id || !$nuova_data) {
                throw new \Exception('Parametri mancanti');
            }

            if ($this->praticheModel->updateDataScadenza($id, $nuova_data, $tipo)) {
                // Crea notifica per aggiornamento scadenza
                $this->notificationService->createNotificaPratica($id, 'scadenza_aggiornata', $_SESSION['user_id'] ?? 1);

                $this->jsonResponse([
                    'success' => true,
                    'message' => 'Data scadenza aggiornata con successo'
                ]);
            } else {
                throw new \Exception('Errore durante l\'aggiornamento della data scadenza');
            }

        } catch (\Exception $e) {
            error_log("Errore in PraticheController::updateScadenza(): " . $e->getMessage());
            $this->jsonResponse(['error' => $e->getMessage()], 400);
        }
    }
}
