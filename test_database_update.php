<?php
// test_database_update.php - Test completo post-aggiornamento database
session_start();

// Carica il bootstrap
require_once __DIR__ . '/bootstrap.php';

use App\Config\Database;
use App\Models\PraticheModel;
use App\Services\NotificationService;

echo "<h1>🧪 Test Completo Post-Aggiornamento Database</h1>";
echo "<p><strong>Data Test:</strong> " . date('Y-m-d H:i:s') . "</p>";

$test_results = [];
$all_tests_passed = true;

try {
    $db = Database::getInstance();
    $praticheModel = new PraticheModel();
    $notificationService = new NotificationService();
    
    echo "<h2>📋 TEST 1: Verifica Struttura Database</h2>";
    
    // Test 1.1: Stati pratiche
    echo "<h3>1.1 Test Stati Pratiche</h3>";
    try {
        $stmt = $db->query("SHOW COLUMNS FROM pratiche LIKE 'stato'");
        $column = $stmt->fetch(PDO::FETCH_ASSOC);
        preg_match_all("/'([^']+)'/", $column['Type'], $matches);
        $stati_disponibili = $matches[1];
        
        $stati_richiesti = ['in_attesa', 'in_revisione', 'approvata', 'completata', 'sospesa', 'respinta'];
        $stati_mancanti = array_diff($stati_richiesti, $stati_disponibili);
        
        if (empty($stati_mancanti)) {
            echo "<p>✅ <strong>Stati pratiche:</strong> Tutti i 6 stati richiesti sono disponibili</p>";
            $test_results['stati_pratiche'] = true;
        } else {
            echo "<p>❌ <strong>Stati mancanti:</strong> " . implode(', ', $stati_mancanti) . "</p>";
            $test_results['stati_pratiche'] = false;
            $all_tests_passed = false;
        }
    } catch (Exception $e) {
        echo "<p>❌ <strong>Errore test stati:</strong> " . $e->getMessage() . "</p>";
        $test_results['stati_pratiche'] = false;
        $all_tests_passed = false;
    }
    
    // Test 1.2: Tabelle notifiche
    echo "<h3>1.2 Test Tabelle Notifiche</h3>";
    try {
        $stmt = $db->query("SHOW TABLES LIKE 'notifiche'");
        $notifiche_exists = $stmt->rowCount() > 0;
        
        $stmt = $db->query("SHOW TABLES LIKE 'notifiche_preferenze'");
        $preferenze_exists = $stmt->rowCount() > 0;
        
        if ($notifiche_exists && $preferenze_exists) {
            echo "<p>✅ <strong>Tabelle notifiche:</strong> Entrambe le tabelle esistono</p>";
            $test_results['tabelle_notifiche'] = true;
        } else {
            echo "<p>❌ <strong>Tabelle mancanti:</strong> ";
            if (!$notifiche_exists) echo "notifiche ";
            if (!$preferenze_exists) echo "notifiche_preferenze ";
            echo "</p>";
            $test_results['tabelle_notifiche'] = false;
            $all_tests_passed = false;
        }
    } catch (Exception $e) {
        echo "<p>❌ <strong>Errore test tabelle:</strong> " . $e->getMessage() . "</p>";
        $test_results['tabelle_notifiche'] = false;
        $all_tests_passed = false;
    }
    
    // Test 1.3: Indici performance
    echo "<h3>1.3 Test Indici Performance</h3>";
    try {
        $stmt = $db->query("SHOW INDEX FROM pratiche WHERE Key_name LIKE 'idx_%'");
        $indici = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($indici) >= 6) {
            echo "<p>✅ <strong>Indici performance:</strong> " . count($indici) . " indici creati</p>";
            $test_results['indici_performance'] = true;
        } else {
            echo "<p>❌ <strong>Indici insufficienti:</strong> Solo " . count($indici) . " trovati, 6 richiesti</p>";
            $test_results['indici_performance'] = false;
            $all_tests_passed = false;
        }
    } catch (Exception $e) {
        echo "<p>❌ <strong>Errore test indici:</strong> " . $e->getMessage() . "</p>";
        $test_results['indici_performance'] = false;
        $all_tests_passed = false;
    }
    
    echo "<h2>🔧 TEST 2: Funzionalità PraticheModel</h2>";
    
    // Test 2.1: CRUD base
    echo "<h3>2.1 Test CRUD Pratiche</h3>";
    try {
        // Test inserimento
        $test_data = [
            'progetto_id' => 1,
            'tipo_documento' => 'SCIA',
            'numero_pratica' => 'TEST-' . time(),
            'stato' => 'in_attesa',
            'note' => 'Test post-aggiornamento database'
        ];
        
        $insert_result = $praticheModel->insertPratica($test_data);
        
        if ($insert_result) {
            echo "<p>✅ <strong>Inserimento pratica:</strong> Successo</p>";
            
            // Test recupero
            $pratiche = $praticheModel->getAllPratiche();
            echo "<p>✅ <strong>Recupero pratiche:</strong> " . count($pratiche) . " pratiche trovate</p>";
            
            $test_results['crud_pratiche'] = true;
        } else {
            echo "<p>❌ <strong>Inserimento pratica:</strong> Fallito</p>";
            $test_results['crud_pratiche'] = false;
            $all_tests_passed = false;
        }
    } catch (Exception $e) {
        echo "<p>❌ <strong>Errore CRUD pratiche:</strong> " . $e->getMessage() . "</p>";
        $test_results['crud_pratiche'] = false;
        $all_tests_passed = false;
    }
    
    // Test 2.2: Workflow stati
    echo "<h3>2.2 Test Workflow Stati</h3>";
    try {
        // Test transizioni consentite
        $can_transition = $praticheModel->canTransition('in_attesa', 'in_revisione');
        $cannot_transition = $praticheModel->canTransition('completata', 'in_attesa');
        
        if ($can_transition && !$cannot_transition) {
            echo "<p>✅ <strong>Workflow transizioni:</strong> Validazione corretta</p>";
            
            // Test stati disponibili
            $stati = $praticheModel->getStatiDisponibili();
            if (count($stati) == 6) {
                echo "<p>✅ <strong>Stati disponibili:</strong> " . count($stati) . " stati</p>";
                $test_results['workflow_stati'] = true;
            } else {
                echo "<p>❌ <strong>Stati disponibili:</strong> " . count($stati) . " invece di 6</p>";
                $test_results['workflow_stati'] = false;
                $all_tests_passed = false;
            }
        } else {
            echo "<p>❌ <strong>Workflow transizioni:</strong> Validazione errata</p>";
            $test_results['workflow_stati'] = false;
            $all_tests_passed = false;
        }
    } catch (Exception $e) {
        echo "<p>❌ <strong>Errore workflow:</strong> " . $e->getMessage() . "</p>";
        $test_results['workflow_stati'] = false;
        $all_tests_passed = false;
    }
    
    // Test 2.3: Scadenze
    echo "<h3>2.3 Test Gestione Scadenze</h3>";
    try {
        $pratiche_scadenza = $praticheModel->getPraticheInScadenza(30);
        $pratiche_critiche = $praticheModel->getPraticheCritiche();
        
        echo "<p>✅ <strong>Pratiche in scadenza (30gg):</strong> " . count($pratiche_scadenza) . "</p>";
        echo "<p>✅ <strong>Pratiche critiche (3gg):</strong> " . count($pratiche_critiche) . "</p>";
        $test_results['gestione_scadenze'] = true;
    } catch (Exception $e) {
        echo "<p>❌ <strong>Errore scadenze:</strong> " . $e->getMessage() . "</p>";
        $test_results['gestione_scadenze'] = false;
        $all_tests_passed = false;
    }
    
    echo "<h2>🔔 TEST 3: Sistema Notifiche</h2>";
    
    // Test 3.1: Creazione notifiche
    echo "<h3>3.1 Test Creazione Notifiche</h3>";
    try {
        $notifica_data = [
            'user_id' => 1,
            'tipo' => 'sistema',
            'titolo' => 'Test post-aggiornamento',
            'messaggio' => 'Test notifica dopo aggiornamento database',
            'priorita' => 'media'
        ];
        
        $notifica_result = $notificationService->createNotifica($notifica_data);
        
        if ($notifica_result) {
            echo "<p>✅ <strong>Creazione notifica:</strong> Successo</p>";
            $test_results['creazione_notifiche'] = true;
        } else {
            echo "<p>❌ <strong>Creazione notifica:</strong> Fallito</p>";
            $test_results['creazione_notifiche'] = false;
            $all_tests_passed = false;
        }
    } catch (Exception $e) {
        echo "<p>❌ <strong>Errore creazione notifica:</strong> " . $e->getMessage() . "</p>";
        $test_results['creazione_notifiche'] = false;
        $all_tests_passed = false;
    }
    
    // Test 3.2: Notifiche specifiche pratiche
    echo "<h3>3.2 Test Notifiche Pratiche</h3>";
    try {
        $pratica_notifica = $notificationService->notificaPraticaScadenzaCritica(1, 2, 1);
        $documento_notifica = $notificationService->notificaDocumentoCaricato(1, 'test.pdf', 1);
        
        if ($pratica_notifica && $documento_notifica) {
            echo "<p>✅ <strong>Notifiche pratiche:</strong> Tutte le tipologie funzionanti</p>";
            $test_results['notifiche_pratiche'] = true;
        } else {
            echo "<p>❌ <strong>Notifiche pratiche:</strong> Alcune tipologie non funzionanti</p>";
            $test_results['notifiche_pratiche'] = false;
            $all_tests_passed = false;
        }
    } catch (Exception $e) {
        echo "<p>❌ <strong>Errore notifiche pratiche:</strong> " . $e->getMessage() . "</p>";
        $test_results['notifiche_pratiche'] = false;
        $all_tests_passed = false;
    }
    
    // Test 3.3: Controllo scadenze automatico
    echo "<h3>3.3 Test Controllo Scadenze Automatico</h3>";
    try {
        $controllo_result = $notificationService->checkScadenzeAutomatiche(7);
        
        if (isset($controllo_result['success']) || isset($controllo_result['scadenze_controllate'])) {
            echo "<p>✅ <strong>Controllo scadenze:</strong> Funzionante</p>";
            echo "<p><strong>Risultato:</strong> " . json_encode($controllo_result) . "</p>";
            $test_results['controllo_scadenze'] = true;
        } else {
            echo "<p>❌ <strong>Controllo scadenze:</strong> Risultato non valido</p>";
            $test_results['controllo_scadenze'] = false;
            $all_tests_passed = false;
        }
    } catch (Exception $e) {
        echo "<p>❌ <strong>Errore controllo scadenze:</strong> " . $e->getMessage() . "</p>";
        $test_results['controllo_scadenze'] = false;
        $all_tests_passed = false;
    }
    
    echo "<h2>📊 TEST 4: Statistiche e Performance</h2>";
    
    // Test 4.1: Statistiche pratiche
    echo "<h3>4.1 Test Statistiche Pratiche</h3>";
    try {
        $statistiche = $praticheModel->getStatistichePratiche();
        
        if (!empty($statistiche) && isset($statistiche['per_stato'])) {
            echo "<p>✅ <strong>Statistiche pratiche:</strong> Dati disponibili</p>";
            echo "<p><strong>Stati con pratiche:</strong> " . count($statistiche['per_stato']) . "</p>";
            $test_results['statistiche_pratiche'] = true;
        } else {
            echo "<p>❌ <strong>Statistiche pratiche:</strong> Dati non disponibili</p>";
            $test_results['statistiche_pratiche'] = false;
            $all_tests_passed = false;
        }
    } catch (Exception $e) {
        echo "<p>❌ <strong>Errore statistiche:</strong> " . $e->getMessage() . "</p>";
        $test_results['statistiche_pratiche'] = false;
        $all_tests_passed = false;
    }
    
    // Test 4.2: Performance query
    echo "<h3>4.2 Test Performance Query</h3>";
    try {
        $start_time = microtime(true);
        $pratiche_stato = $praticheModel->getPraticheByStato('in_attesa');
        $end_time = microtime(true);
        
        $query_time = ($end_time - $start_time) * 1000; // in millisecondi
        
        echo "<p>✅ <strong>Query per stato:</strong> " . count($pratiche_stato) . " pratiche in " . number_format($query_time, 2) . "ms</p>";

        if ($query_time < 100) { // Meno di 100ms è buono
            echo "<p>✅ <strong>Performance:</strong> Ottima (< 100ms)</p>";
            $test_results['performance_query'] = true;
        } else {
            echo "<p>⚠️ <strong>Performance:</strong> Accettabile (" . number_format($query_time, 2) . "ms)</p>";
            $test_results['performance_query'] = true; // Comunque accettabile
        }
    } catch (Exception $e) {
        echo "<p>❌ <strong>Errore performance:</strong> " . $e->getMessage() . "</p>";
        $test_results['performance_query'] = false;
        $all_tests_passed = false;
    }
    
    echo "<h2>📋 RIEPILOGO RISULTATI TEST</h2>";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'><th>Test</th><th>Risultato</th><th>Stato</th></tr>";
    
    foreach ($test_results as $test_name => $result) {
        $status = $result ? '✅ PASS' : '❌ FAIL';
        $color = $result ? '#d4edda' : '#f8d7da';
        echo "<tr style='background: {$color};'>";
        echo "<td>" . ucfirst(str_replace('_', ' ', $test_name)) . "</td>";
        echo "<td>{$status}</td>";
        echo "<td>" . ($result ? 'Funzionante' : 'Richiede attenzione') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if ($all_tests_passed) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h3>🎉 TUTTI I TEST SUPERATI CON SUCCESSO!</h3>";
        echo "<p><strong>Il database è stato aggiornato correttamente e tutte le funzionalità sono operative.</strong></p>";
        echo "<ul>";
        echo "<li>✅ Workflow pratiche con 6 stati funzionante</li>";
        echo "<li>✅ Sistema notifiche completo e operativo</li>";
        echo "<li>✅ Performance ottimizzate con indici</li>";
        echo "<li>✅ Controllo scadenze automatico attivo</li>";
        echo "<li>✅ Statistiche e dashboard funzionanti</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h3>⚠️ ALCUNI TEST NON SUPERATI</h3>";
        echo "<p>Alcuni test hanno fallito. Controlla i dettagli sopra e risolvi i problemi prima di procedere.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Errore Generale Durante i Test</h2>";
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
    echo "<p><strong>Errore:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . " <strong>Linea:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<h3>🔗 Link Utili:</h3>";
echo "<ul>";
echo "<li><a href='test_pratiche.php' target='_blank'>Test Completo PraticheModel</a></li>";
echo "<li><a href='test_notifiche.php' target='_blank'>Test Completo Sistema Notifiche</a></li>";
echo "<li><a href='pratiche' target='_blank'>Dashboard Pratiche</a></li>";
echo "<li><a href='notifiche' target='_blank'>Dashboard Notifiche</a></li>";
echo "<li><a href='index.php'>← Torna all'applicazione</a></li>";
echo "</ul>";

echo "<hr>";
echo "<p><em>Test completati il " . date('Y-m-d H:i:s') . "</em></p>";
?>
