<?php
// test_notifiche.php - Script di test per il sistema notifiche completo
session_start();

// Carica il bootstrap
require_once __DIR__ . '/bootstrap.php';

use App\Models\NotificheModel;
use App\Services\NotificationService;

echo "<h1>Test Sistema Notifiche Completo</h1>";

try {
    $notificheModel = new NotificheModel();
    $notificationService = new NotificationService();
    
    echo "<h2>1. Test NotificheModel - Metodi Base</h2>";
    
    // Test inserimento notifica
    $testData = [
        'user_id' => 1,
        'tipo' => 'test',
        'titolo' => 'Notifica di test',
        'messaggio' => 'Questo è un messaggio di test per verificare il funzionamento del sistema',
        'priorita' => 'media',
        'link_azione' => '/test',
        'metadata' => ['test' => true, 'timestamp' => time()]
    ];
    
    $notificaId = $notificheModel->insertNotifica($testData);
    echo "Notifica inserita con ID: " . ($notificaId ? $notificaId : "❌ ERRORE") . "<br>";
    
    // Test recupero notifiche non lette
    $nonLette = $notificheModel->getNotificheNonLette(1, 10);
    echo "Notifiche non lette trovate: " . count($nonLette) . "<br>";
    
    // Test conteggio notifiche non lette
    $count = $notificheModel->countNotificheNonLette(1);
    echo "Conteggio notifiche non lette: " . $count . "<br>";
    
    // Test statistiche
    $stats = $notificheModel->getStatisticheNotifiche(1);
    echo "Statistiche notifiche:<br>";
    echo "<pre>" . print_r($stats, true) . "</pre>";
    
    echo "<h2>2. Test Gestione Preferenze</h2>";
    
    // Test impostazione preferenze
    $prefResult = $notificheModel->setPreferenzaUtente(1, 'test', true, true, 5);
    echo "Preferenza impostata: " . ($prefResult ? "✅ OK" : "❌ ERRORE") . "<br>";
    
    // Test recupero preferenza email
    $emailPref = $notificheModel->getPreferenzaEmail(1, 'test');
    echo "Preferenza email per 'test': " . ($emailPref ? "✅ Abilitata" : "❌ Disabilitata") . "<br>";
    
    // Test recupero tutte le preferenze
    $allPrefs = $notificheModel->getPreferenzeUtente(1);
    echo "Preferenze utente configurate: " . count($allPrefs) . "<br>";
    
    echo "<h2>3. Test Azioni Dashboard</h2>";
    
    if ($notificaId) {
        // Test marca come letta
        $readResult = $notificheModel->markAsRead($notificaId, 1);
        echo "Notifica marcata come letta: " . ($readResult ? "✅ OK" : "❌ ERRORE") . "<br>";
        
        // Verifica conteggio aggiornato
        $newCount = $notificheModel->countNotificheNonLette(1);
        echo "Nuovo conteggio notifiche non lette: " . $newCount . "<br>";
    }
    
    echo "<h2>4. Test NotificationService</h2>";
    
    // Test creazione notifica tramite service
    $serviceData = [
        'user_id' => 1,
        'tipo' => 'servizio',
        'titolo' => 'Test NotificationService',
        'messaggio' => 'Notifica creata tramite NotificationService',
        'priorita' => 'alta'
    ];
    
    $serviceResult = $notificationService->createNotifica($serviceData);
    echo "Notifica creata tramite service: " . ($serviceResult ? "✅ OK" : "❌ ERRORE") . "<br>";
    
    // Test controllo scadenze automatiche
    echo "<h3>Test Controllo Scadenze Automatiche</h3>";
    $scadenzeResult = $notificationService->checkScadenzeAutomatiche(30);
    echo "Risultato controllo scadenze:<br>";
    echo "<pre>" . print_r($scadenzeResult, true) . "</pre>";
    
    // Test notifiche specifiche
    echo "<h3>Test Notifiche Specifiche</h3>";
    
    $progettoResult = $notificationService->notificaCambioStatoProgetto(1, 'completato', 1);
    echo "Notifica cambio stato progetto: " . ($progettoResult ? "✅ OK" : "❌ ERRORE") . "<br>";
    
    $clienteResult = $notificationService->notificaNuovoCliente(1, 'Mario Rossi', 1);
    echo "Notifica nuovo cliente: " . ($clienteResult ? "✅ OK" : "❌ ERRORE") . "<br>";
    
    $documentoResult = $notificationService->notificaDocumentoCaricato(1, 'documento_test.pdf', 1);
    echo "Notifica documento caricato: " . ($documentoResult ? "✅ OK" : "❌ ERRORE") . "<br>";
    
    echo "<h2>5. Test Funzionalità Avanzate</h2>";
    
    // Test recupero scadenze imminenti
    $scadenze = $notificheModel->getScadenzeImminenti(7);
    echo "Scadenze imminenti (7 giorni): " . count($scadenze) . "<br>";
    
    // Test recupero pratiche in scadenza
    $pratiche = $notificheModel->getPraticheInScadenza(7);
    echo "Pratiche in scadenza (7 giorni): " . count($pratiche) . "<br>";
    
    // Test verifica esistenza notifica
    if ($notificaId) {
        $esiste = $notificheModel->esisteNotifica('test', $notificaId, 1);
        echo "Verifica esistenza notifica: " . ($esiste ? "✅ Esiste" : "❌ Non esiste") . "<br>";
    }
    
    echo "<h2>6. Test Pulizia e Manutenzione</h2>";
    
    // Test pulizia notifiche vecchie (0 giorni per test)
    $deleted = $notificheModel->deleteOldNotifiche(0);
    echo "Notifiche eliminate nella pulizia: " . $deleted . "<br>";
    
    // Test controlli schedulati
    $scheduledResult = $notificationService->runScheduledChecks();
    echo "Risultato controlli schedulati:<br>";
    echo "<pre>" . print_r($scheduledResult, true) . "</pre>";
    
    echo "<h2>7. Test Compatibilità Metodi Legacy</h2>";
    
    // Test metodi di compatibilità
    $legacyData = [
        'user_id' => 1,
        'tipo' => 'legacy',
        'titolo' => 'Test compatibilità',
        'messaggio' => 'Test metodi legacy'
    ];
    
    $legacyId = $notificheModel->create($legacyData);
    echo "Notifica creata con metodo legacy: " . ($legacyId ? "✅ OK (ID: {$legacyId})" : "❌ ERRORE") . "<br>";
    
    if ($legacyId) {
        $legacyRead = $notificheModel->segnaComeLetta($legacyId, 1);
        echo "Notifica marcata come letta (metodo legacy): " . ($legacyRead ? "✅ OK" : "❌ ERRORE") . "<br>";
    }
    
    echo "<h2>8. Riepilogo Finale</h2>";
    
    // Statistiche finali
    $finalStats = $notificheModel->getStatisticheNotifiche(1);
    echo "Statistiche finali:<br>";
    echo "- Totali: " . ($finalStats['conteggi']['totali'] ?? 0) . "<br>";
    echo "- Non lette: " . ($finalStats['conteggi']['non_lette'] ?? 0) . "<br>";
    echo "- Lette: " . ($finalStats['conteggi']['lette'] ?? 0) . "<br>";
    
    // Test marca tutte come lette
    $markAllResult = $notificheModel->markAllAsRead(1);
    echo "Tutte le notifiche marcate come lette: " . ($markAllResult ? "✅ OK" : "❌ ERRORE") . "<br>";
    
    // Conteggio finale
    $finalCount = $notificheModel->countNotificheNonLette(1);
    echo "Conteggio finale notifiche non lette: " . $finalCount . "<br>";
    
    echo "<h2>✅ Test Completati con Successo!</h2>";
    echo "<p>Il sistema di notifiche è stato testato e funziona correttamente.</p>";
    
    echo "<h3>Funzionalità Testate:</h3>";
    echo "<ul>";
    echo "<li>✅ Creazione e gestione notifiche</li>";
    echo "<li>✅ Gestione preferenze utente</li>";
    echo "<li>✅ Dashboard e statistiche</li>";
    echo "<li>✅ Controllo automatico scadenze</li>";
    echo "<li>✅ Notifiche specifiche per eventi</li>";
    echo "<li>✅ Pulizia automatica</li>";
    echo "<li>✅ Compatibilità metodi legacy</li>";
    echo "</ul>";

} catch (Exception $e) {
    echo "<h2>❌ Errore durante i test</h2>";
    echo "<p>Errore: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>File: " . $e->getFile() . " Linea: " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='index.php'>← Torna all'applicazione</a></p>";
echo "<p><a href='test_csrf.php'>Test CSRF Protection</a></p>";
?>
