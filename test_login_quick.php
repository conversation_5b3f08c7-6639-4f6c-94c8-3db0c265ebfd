<?php
// test_login_quick.php - Test rapido login e funzionalità base
session_start();

echo "<h1>🧪 Test Rapido Login e Funzionalità</h1>";
echo "<p><strong>Data Test:</strong> " . date('Y-m-d H:i:s') . "</p>";

try {
    // Test 1: Connessione Database
    echo "<h2>1. Test Connessione Database</h2>";
    $pdo = new PDO("mysql:host=localhost;dbname=studio_tecnico;charset=utf8mb4", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p>✅ <strong>Connessione database:</strong> OK</p>";
    
    // Test 2: Verifica Utenti
    echo "<h2>2. Test Utenti</h2>";
    $stmt = $pdo->query("SELECT id, username, email FROM users LIMIT 3");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<p><strong>Utenti disponibili:</strong></p>";
    echo "<ul>";
    foreach ($users as $user) {
        echo "<li>ID: {$user['id']}, Username: {$user['username']}, Email: {$user['email']}</li>";
    }
    echo "</ul>";
    
    // Test 3: Verifica Password Admin
    echo "<h2>3. Test Password Admin</h2>";
    $stmt = $pdo->prepare("SELECT id, username, password FROM users WHERE username = 'admin'");
    $stmt->execute();
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin) {
        echo "<p>✅ <strong>Utente admin trovato:</strong> ID {$admin['id']}</p>";
        
        // Test password
        $test_password = 'admin123';
        if (password_verify($test_password, $admin['password'])) {
            echo "<p>✅ <strong>Password admin123:</strong> Corretta</p>";
        } else {
            echo "<p>❌ <strong>Password admin123:</strong> Non corretta</p>";
            
            // Aggiorna password
            $new_hash = password_hash($test_password, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
            $stmt->execute([$new_hash, $admin['id']]);
            echo "<p>🔧 <strong>Password aggiornata a:</strong> admin123</p>";
        }
    } else {
        echo "<p>❌ <strong>Utente admin:</strong> Non trovato</p>";
    }
    
    // Test 4: Verifica Tabelle Critiche
    echo "<h2>4. Test Tabelle Critiche</h2>";
    $tables_to_check = ['pratiche', 'progetti', 'clienti', 'notifiche', 'notifiche_preferenze'];
    
    foreach ($tables_to_check as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM `$table`");
            $count = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "<p>✅ <strong>Tabella {$table}:</strong> {$count['count']} record</p>";
        } catch (Exception $e) {
            echo "<p>❌ <strong>Tabella {$table}:</strong> Errore - " . $e->getMessage() . "</p>";
        }
    }
    
    // Test 5: Verifica Stati Pratiche
    echo "<h2>5. Test Stati Pratiche</h2>";
    try {
        $stmt = $pdo->query("SHOW COLUMNS FROM pratiche LIKE 'stato'");
        $column = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($column) {
            preg_match_all("/'([^']+)'/", $column['Type'], $matches);
            $stati = $matches[1];
            echo "<p>✅ <strong>Stati disponibili:</strong> " . implode(', ', $stati) . " (" . count($stati) . " totali)</p>";
            
            if (count($stati) >= 6) {
                echo "<p>✅ <strong>Workflow pratiche:</strong> Completo</p>";
            } else {
                echo "<p>⚠️ <strong>Workflow pratiche:</strong> Incompleto</p>";
            }
        }
    } catch (Exception $e) {
        echo "<p>❌ <strong>Errore stati pratiche:</strong> " . $e->getMessage() . "</p>";
    }
    
    // Test 6: Test Caricamento Classi
    echo "<h2>6. Test Caricamento Classi</h2>";
    
    // Carica bootstrap
    if (file_exists(__DIR__ . '/bootstrap.php')) {
        try {
            require_once __DIR__ . '/bootstrap.php';
            echo "<p>✅ <strong>Bootstrap:</strong> Caricato</p>";
            
            // Test classi principali
            $classes = [
                'App\\Config\\Database',
                'App\\Models\\PraticheModel',
                'App\\Services\\NotificationService'
            ];
            
            foreach ($classes as $class) {
                if (class_exists($class)) {
                    echo "<p>✅ <strong>Classe {$class}:</strong> Disponibile</p>";
                } else {
                    echo "<p>❌ <strong>Classe {$class}:</strong> Non trovata</p>";
                }
            }
            
        } catch (Exception $e) {
            echo "<p>❌ <strong>Errore bootstrap:</strong> " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p>❌ <strong>Bootstrap:</strong> File non trovato</p>";
    }
    
    // Test 7: Simulazione Login
    echo "<h2>7. Test Simulazione Login</h2>";
    
    if ($admin) {
        // Simula login
        $_SESSION['user_id'] = $admin['id'];
        $_SESSION['username'] = $admin['username'];
        $_SESSION['logged_in'] = true;
        
        echo "<p>✅ <strong>Sessione login simulata:</strong> OK</p>";
        echo "<p><strong>User ID:</strong> {$_SESSION['user_id']}</p>";
        echo "<p><strong>Username:</strong> {$_SESSION['username']}</p>";
        
        // Test accesso a pagina protetta
        echo "<p><a href='pratiche' target='_blank'>🔗 Test Accesso Dashboard Pratiche</a></p>";
        echo "<p><a href='progetti' target='_blank'>🔗 Test Accesso Dashboard Progetti</a></p>";
        echo "<p><a href='notifiche' target='_blank'>🔗 Test Accesso Dashboard Notifiche</a></p>";
    }
    
    echo "<h2>✅ RIEPILOGO TEST</h2>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3>🎉 Sistema Funzionante!</h3>";
    echo "<p><strong>Credenziali di accesso:</strong></p>";
    echo "<p><strong>Username:</strong> admin<br>";
    echo "<strong>Password:</strong> admin123</p>";
    echo "<p><a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 Vai al Login</a></p>";
    echo "</div>";
    
    echo "<h3>🧪 Test Aggiuntivi:</h3>";
    echo "<ul>";
    echo "<li><a href='test_pratiche.php' target='_blank'>Test Completo PraticheModel</a></li>";
    echo "<li><a href='test_notifiche.php' target='_blank'>Test Sistema Notifiche</a></li>";
    echo "<li><a href='emergency_system_check.php' target='_blank'>Controllo Sistema Completo</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2>❌ Errore Durante i Test</h2>";
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
    echo "<p><strong>Errore:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . " <strong>Linea:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
    
    echo "<p><a href='emergency_database_fix.php'>🔧 Esegui Correzione Emergency</a></p>";
}

echo "<hr>";
echo "<p><a href='index.php'>← Torna all'applicazione</a></p>";
echo "<p><em>Test completato il " . date('Y-m-d H:i:s') . "</em></p>";
?>
