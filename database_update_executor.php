<?php
// database_update_executor.php - Esecutore aggiornamento database Studio Tecnico
session_start();

// Carica il bootstrap
require_once __DIR__ . '/bootstrap.php';

use App\Config\Database;

echo "<h1>🚀 Aggiornamento Database Studio Tecnico</h1>";
echo "<p><strong>Data:</strong> " . date('Y-m-d H:i:s') . "</p>";

try {
    $db = Database::getInstance();
    
    echo "<h2>📋 FASE 1: Verifica Stato Attuale</h2>";
    
    // Verifica versione database
    $stmt = $db->query("SELECT VERSION() as version");
    $version = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p>✅ <strong>Versione Database:</strong> " . $version['version'] . "</p>";
    
    // Verifica stati pratiche attuali
    echo "<h3>Stati Pratiche Attuali:</h3>";
    $stmt = $db->query("SHOW COLUMNS FROM pratiche LIKE 'stato'");
    $column = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p><strong>Enum attuale:</strong> <code>" . $column['Type'] . "</code></p>";
    
    $stmt = $db->query("SELECT stato, COUNT(*) as count FROM pratiche GROUP BY stato");
    $stati = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Stato</th><th>Conteggio</th></tr>";
    foreach ($stati as $stato) {
        echo "<tr><td>{$stato['stato']}</td><td>{$stato['count']}</td></tr>";
    }
    echo "</table>";
    
    // Verifica esistenza tabelle notifiche
    echo "<h3>Verifica Tabelle Notifiche:</h3>";
    $stmt = $db->query("SHOW TABLES LIKE 'notifiche%'");
    $tabelle_notifiche = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tabelle_notifiche)) {
        echo "<p>❌ <strong>Tabelle notifiche NON esistono</strong> - Devono essere create</p>";
    } else {
        echo "<p>✅ <strong>Tabelle notifiche esistenti:</strong> " . implode(', ', $tabelle_notifiche) . "</p>";
    }
    
    echo "<h2>🔧 FASE 2: Backup Automatico</h2>";
    
    // Crea backup tabella pratiche
    $db->exec("CREATE TABLE IF NOT EXISTS pratiche_backup_" . date('Ymd_His') . " AS SELECT * FROM pratiche");
    echo "<p>✅ <strong>Backup pratiche creato</strong></p>";
    
    echo "<h2>⚡ FASE 3: Esecuzione Aggiornamenti</h2>";
    
    // Inizia transazione per sicurezza
    $db->beginTransaction();
    
    try {
        // 3.1 Aggiornamento enum stati pratiche
        echo "<h3>3.1 Aggiornamento Stati Pratiche</h3>";
        $sql = "ALTER TABLE `pratiche` 
                MODIFY COLUMN `stato` ENUM(
                    'in_attesa',
                    'in_revisione', 
                    'approvata',
                    'completata',
                    'sospesa',
                    'respinta'
                ) DEFAULT 'in_attesa'";
        
        $db->exec($sql);
        echo "<p>✅ <strong>Stati pratiche aggiornati</strong> - 6 stati disponibili</p>";
        
        // 3.2 Creazione indici performance
        echo "<h3>3.2 Creazione Indici Performance</h3>";
        $indici = [
            "CREATE INDEX IF NOT EXISTS idx_pratiche_stato ON pratiche (stato)",
            "CREATE INDEX IF NOT EXISTS idx_pratiche_data_scadenza ON pratiche (data_scadenza)",
            "CREATE INDEX IF NOT EXISTS idx_pratiche_data_scadenza_integrazione ON pratiche (data_scadenza_integrazione)",
            "CREATE INDEX IF NOT EXISTS idx_pratiche_stato_data ON pratiche (stato, data_apertura)",
            "CREATE INDEX IF NOT EXISTS idx_pratiche_ente ON pratiche (ente_riferimento)",
            "CREATE INDEX IF NOT EXISTS idx_pratiche_responsabile ON pratiche (responsabile)"
        ];
        
        foreach ($indici as $indice) {
            $db->exec($indice);
        }
        echo "<p>✅ <strong>Indici performance creati</strong> - 6 indici aggiunti</p>";
        
        // 3.3 Creazione tabella notifiche
        echo "<h3>3.3 Creazione Sistema Notifiche</h3>";
        
        $sql_notifiche = "CREATE TABLE IF NOT EXISTS `notifiche` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `user_id` int(11) NOT NULL,
          `tipo` enum('scadenza','pratica','progetto','sistema','fattura','cliente','documento') NOT NULL,
          `titolo` varchar(255) NOT NULL,
          `messaggio` text NOT NULL,
          `priorita` enum('bassa','media','alta') DEFAULT 'media',
          `link_azione` varchar(255) DEFAULT NULL,
          `metadata` json DEFAULT NULL,
          `letta` boolean DEFAULT FALSE,
          `data_creazione` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          `data_lettura` timestamp NULL DEFAULT NULL,
          PRIMARY KEY (`id`),
          KEY `idx_user_letta` (`user_id`, `letta`),
          KEY `idx_data_creazione` (`data_creazione`),
          KEY `idx_tipo` (`tipo`),
          KEY `idx_priorita` (`priorita`),
          CONSTRAINT `fk_notifiche_user` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->exec($sql_notifiche);
        echo "<p>✅ <strong>Tabella notifiche creata</strong></p>";
        
        // 3.4 Creazione tabella preferenze
        $sql_preferenze = "CREATE TABLE IF NOT EXISTS `notifiche_preferenze` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `user_id` int(11) NOT NULL,
          `tipo_notifica` varchar(50) NOT NULL,
          `email_enabled` boolean DEFAULT TRUE,
          `push_enabled` boolean DEFAULT TRUE,
          `soglia_giorni` int(11) DEFAULT 7,
          `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          UNIQUE KEY `unique_user_tipo` (`user_id`, `tipo_notifica`),
          KEY `idx_user_id` (`user_id`),
          CONSTRAINT `fk_notifiche_preferenze_user` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->exec($sql_preferenze);
        echo "<p>✅ <strong>Tabella preferenze notifiche creata</strong></p>";
        
        echo "<h2>📊 FASE 4: Configurazione Dati Iniziali</h2>";
        
        // 4.1 Preferenze default per utenti esistenti
        echo "<h3>4.1 Configurazione Preferenze Utenti</h3>";
        $sql_preferenze_default = "INSERT IGNORE INTO `notifiche_preferenze` (`user_id`, `tipo_notifica`, `email_enabled`, `push_enabled`, `soglia_giorni`)
        SELECT 
            u.id,
            tipo.tipo_notifica,
            TRUE,
            TRUE,
            CASE 
                WHEN tipo.tipo_notifica IN ('scadenza', 'pratica') THEN 7
                WHEN tipo.tipo_notifica = 'sistema' THEN 1
                ELSE 3
            END
        FROM `users` u
        CROSS JOIN (
            SELECT 'scadenza' as tipo_notifica
            UNION SELECT 'pratica'
            UNION SELECT 'progetto' 
            UNION SELECT 'sistema'
            UNION SELECT 'fattura'
            UNION SELECT 'cliente'
            UNION SELECT 'documento'
        ) tipo";
        
        $db->exec($sql_preferenze_default);
        
        $stmt = $db->query("SELECT COUNT(*) as count FROM notifiche_preferenze");
        $count_preferenze = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p>✅ <strong>Preferenze configurate:</strong> {$count_preferenze['count']} record</p>";
        
        // 4.2 Notifiche di sistema iniziali
        echo "<h3>4.2 Notifiche Sistema Iniziali</h3>";
        $sql_notifiche_sistema = "INSERT IGNORE INTO `notifiche` (`user_id`, `tipo`, `titolo`, `messaggio`, `priorita`, `metadata`)
        VALUES 
        (1, 'sistema', 'Database aggiornato', 'Il database è stato aggiornato con successo per supportare il workflow pratiche e sistema notifiche.', 'media', '{\"tipo_notifica\": \"database_aggiornato\", \"versione\": \"2025-01-05\"}'),
        (1, 'sistema', 'Workflow pratiche attivato', 'Il sistema di workflow automatizzato per le pratiche è ora attivo con 6 stati e transizioni validate.', 'bassa', '{\"tipo_notifica\": \"workflow_attivato\", \"stati_supportati\": 6}'),
        (1, 'sistema', 'Sistema notifiche attivato', 'Il sistema di notifiche automatiche è stato configurato e attivato con successo.', 'bassa', '{\"tipo_notifica\": \"notifiche_attivate\"}')";
        
        $db->exec($sql_notifiche_sistema);
        echo "<p>✅ <strong>Notifiche sistema create</strong></p>";
        
        // Commit transazione
        $db->commit();
        echo "<p>✅ <strong>Transazione completata con successo</strong></p>";
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
    
    echo "<h2>🔍 FASE 5: Verifica Risultati</h2>";
    
    // Verifica stati pratiche aggiornati
    echo "<h3>5.1 Verifica Stati Pratiche</h3>";
    $stmt = $db->query("SHOW COLUMNS FROM pratiche LIKE 'stato'");
    $column = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p><strong>Enum aggiornato:</strong> <code>" . $column['Type'] . "</code></p>";
    
    // Conta stati disponibili
    preg_match_all("/'([^']+)'/", $column['Type'], $matches);
    $stati_disponibili = $matches[1];
    echo "<p>✅ <strong>Stati disponibili:</strong> " . count($stati_disponibili) . " (" . implode(', ', $stati_disponibili) . ")</p>";
    
    // Verifica tabelle notifiche
    echo "<h3>5.2 Verifica Sistema Notifiche</h3>";
    $stmt = $db->query("SELECT COUNT(*) as count FROM notifiche");
    $count_notifiche = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p>✅ <strong>Notifiche totali:</strong> {$count_notifiche['count']}</p>";
    
    $stmt = $db->query("SELECT COUNT(*) as count FROM notifiche_preferenze");
    $count_preferenze = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p>✅ <strong>Preferenze configurate:</strong> {$count_preferenze['count']}</p>";
    
    // Verifica indici
    echo "<h3>5.3 Verifica Indici Performance</h3>";
    $stmt = $db->query("SHOW INDEX FROM pratiche WHERE Key_name LIKE 'idx_%'");
    $indici = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<p>✅ <strong>Indici performance:</strong> " . count($indici) . " creati</p>";
    
    echo "<h2>✅ AGGIORNAMENTO COMPLETATO CON SUCCESSO!</h2>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3>🎯 Funzionalità Ora Disponibili:</h3>";
    echo "<ul>";
    echo "<li>✅ <strong>Workflow Pratiche</strong> - 6 stati con transizioni validate</li>";
    echo "<li>✅ <strong>Sistema Notifiche</strong> - Completo e configurato</li>";
    echo "<li>✅ <strong>Controllo Scadenze</strong> - Automatico</li>";
    echo "<li>✅ <strong>Dashboard Statistiche</strong> - Avanzate</li>";
    echo "<li>✅ <strong>Performance</strong> - Ottimizzate con indici</li>";
    echo "<li>✅ <strong>Preferenze Utente</strong> - Personalizzabili</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>🧪 Prossimi Passi:</h3>";
    echo "<ol>";
    echo "<li><a href='test_pratiche.php' target='_blank'>Test PraticheModel e Workflow</a></li>";
    echo "<li><a href='test_notifiche.php' target='_blank'>Test Sistema Notifiche</a></li>";
    echo "<li><a href='pratiche' target='_blank'>Dashboard Pratiche Aggiornata</a></li>";
    echo "<li><a href='notifiche' target='_blank'>Dashboard Notifiche</a></li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<h2>❌ Errore Durante l'Aggiornamento</h2>";
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
    echo "<p><strong>Errore:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . " <strong>Linea:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>Soluzione:</strong> Controlla i log e riprova. Il backup è disponibile per il rollback.</p>";
    echo "</div>";
    
    echo "<h3>🔄 Rollback Disponibile:</h3>";
    echo "<p>In caso di problemi, i dati originali sono salvati nelle tabelle di backup create automaticamente.</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>← Torna all'applicazione</a></p>";
echo "<p><em>Aggiornamento eseguito il " . date('Y-m-d H:i:s') . "</em></p>";
?>
