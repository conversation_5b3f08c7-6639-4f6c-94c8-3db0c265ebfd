<?php
session_start();
require_once 'error_log.php';

// Simuliamo un utente loggato
$_SESSION['user_name'] = 'TestUser';

// Test 1: Log Informativo
logError("Test messaggio informativo", "INFO");

// Test 2: Log Warning
logError("Attenzione: memoria in esaurimento", "WARNING");

// Test 3: Log Error
logError("Errore durante il salvataggio del file", "ERROR");

// Test 4: Log Fatal
logError("Database non raggiungibile", "FATAL ERROR");

// Test 5: Log Query
logQuery(
    "SELECT * FROM users WHERE id = :id AND status = :status",
    ['id' => 1, 'status' => 'active']
);

// Test 6: Generazione di un Notice
$undefined_variable;

// Test 7: Generazione di un Warning
$array = [];
$value = $array[0];

// Test 8: Generazione di un'eccezione
try {
    throw new Exception("Questa è un'eccezione di test");
} catch (Exception $e) {
    logError($e->getMessage(), "ERROR");
}

echo "Test completati! Controlla il file errors.log";
