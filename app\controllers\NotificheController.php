<?php

namespace App\Controllers;

use App\Core\Controller;
use App\Models\Notifica;

class NotificheController extends Controller {
    private $notifica;

    public function __construct() {
        parent::__construct();
        $this->notifica = new Notifica();
    }

    public function index() {
        $this->render('notifiche/index', [
            'notifiche' => $this->notifica->getNotificheUtente($_SESSION['utente_id']),
            'preferenze' => $this->notifica->getPreferenzeUtente($_SESSION['utente_id'])
        ]);
    }

    public function getUltime() {
        try {
            $notifiche = $this->notifica->getNotificheUtente(
                $_SESSION['utente_id'],
                'non_letta',
                5
            );

            $this->jsonResponse([
                'success' => true,
                'notifiche' => array_map(function($n) {
                    return [
                        'id' => $n['id'],
                        'titolo' => $n['titolo'],
                        'messaggio' => $n['messaggio'],
                        'data' => date('d/m/Y H:i', strtotime($n['data_creazione'])),
                        'priorita' => $n['priorita'],
                        'link' => $n['link']
                    ];
                }, $notifiche)
            ]);
        } catch (\Exception $e) {
            $this->jsonResponse(['error' => $e->getMessage()], 500);
        }
    }

    public function segnaLetta($id) {
        try {
            if (!$this->security->validateCSRFToken()) {
                throw new \Exception('Token CSRF non valido');
            }

            if ($this->notifica->segnaComeLetta($id, $_SESSION['utente_id'])) {
                $this->jsonResponse(['success' => true]);
            } else {
                throw new \Exception('Errore nell\'aggiornamento della notifica');
            }
        } catch (\Exception $e) {
            $this->jsonResponse(['error' => $e->getMessage()], 400);
        }
    }

    public function archivia($id) {
        try {
            if (!$this->security->validateCSRFToken()) {
                throw new \Exception('Token CSRF non valido');
            }

            if ($this->notifica->archivia($id)) {
                $this->jsonResponse(['success' => true]);
            } else {
                throw new \Exception('Errore nell\'archiviazione della notifica');
            }
        } catch (\Exception $e) {
            $this->jsonResponse(['error' => $e->getMessage()], 400);
        }
    }

    public function salvaPreferenze() {
        try {
            if (!$this->security->validateCSRFToken()) {
                throw new \Exception('Token CSRF non valido');
            }

            $preferenze = filter_input(INPUT_POST, 'preferenze', FILTER_DEFAULT, FILTER_REQUIRE_ARRAY);
            if (!$preferenze) {
                throw new \Exception('Dati non validi');
            }

            $this->notifica->updatePreferenze($_SESSION['utente_id'], $preferenze);
            $this->jsonResponse(['success' => true]);
        } catch (\Exception $e) {
            $this->jsonResponse(['error' => $e->getMessage()], 400);
        }
    }

    public function verificaScadenze() {
        try {
            $scadenze = $this->notifica->getScadenzeImminenti();
            
            foreach ($scadenze as $scadenza) {
                if ($scadenza['data_scadenza']) {
                    $this->notifica->creaNotificaScadenza(
                        $scadenza,
                        'scadenza_pratica',
                        $scadenza['data_scadenza']
                    );
                }
                
                if ($scadenza['data_scadenza_integrazione']) {
                    $this->notifica->creaNotificaScadenza(
                        $scadenza,
                        'scadenza_integrazione',
                        $scadenza['data_scadenza_integrazione']
                    );
                }
            }

            $this->jsonResponse([
                'success' => true,
                'message' => 'Verifica scadenze completata',
                'scadenze_trovate' => count($scadenze)
            ]);
        } catch (\Exception $e) {
            $this->jsonResponse(['error' => $e->getMessage()], 500);
        }
    }

    private function jsonResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}
