<?php
namespace App\Core;

class Security {
    public static function generateCsrfToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }

    public static function validateCsrfToken($token) {
        if (!isset($_SESSION['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $token)) {
            throw new \Exception('CSRF token non valido');
        }
        return true;
    }

    public static function csrfField() {
        $token = self::generateCsrfToken();
        return '<input type="hidden" name="_token" value="' . htmlspecialchars($token) . '">';
    }

    public static function sanitize($data) {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $data[$key] = self::sanitize($value);
            }
        } else {
            $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        }
        return $data;
    }

    public static function validateInput($input, $rules) {
        $errors = [];
        foreach ($rules as $field => $rule) {
            if (strpos($rule, 'required') !== false && empty($input[$field])) {
                $errors[$field] = "Il campo è obbligatorio";
            }
            if (strpos($rule, 'email') !== false && !filter_var($input[$field], FILTER_VALIDATE_EMAIL)) {
                $errors[$field] = "Email non valida";
            }
            if (strpos($rule, 'numeric') !== false && !is_numeric($input[$field])) {
                $errors[$field] = "Il campo deve essere numerico";
            }
            if (preg_match('/min:(\d+)/', $rule, $matches)) {
                $min = $matches[1];
                if (strlen($input[$field]) < $min) {
                    $errors[$field] = "Il campo deve essere lungo almeno $min caratteri";
                }
            }
            if (preg_match('/max:(\d+)/', $rule, $matches)) {
                $max = $matches[1];
                if (strlen($input[$field]) > $max) {
                    $errors[$field] = "Il campo non può superare $max caratteri";
                }
            }
        }
        return $errors;
    }
}
