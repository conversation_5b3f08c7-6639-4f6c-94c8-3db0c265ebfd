<?php
// database_fix_executor.php - Correzione automatica problemi database
session_start();

// Carica il bootstrap
require_once __DIR__ . '/bootstrap.php';

use App\Config\Database;

echo "<h1>🔧 Correzione Automatica Database Studio Tecnico</h1>";
echo "<p><strong>Data Correzione:</strong> " . date('Y-m-d H:i:s') . "</p>";

$fixes_applied = [];
$errors_encountered = [];

try {
    $db = Database::getInstance();
    
    echo "<h2>🔍 FASE 1: Analisi Problemi</h2>";
    
    // Analizza problemi esistenti
    $problems = [];
    
    // 1.1 Verifica stati pratiche
    try {
        $stmt = $db->query("SHOW COLUMNS FROM pratiche LIKE 'stato'");
        $column = $stmt->fetch(PDO::FETCH_ASSOC);
        preg_match_all("/'([^']+)'/", $column['Type'], $matches);
        $stati_attuali = $matches[1];
        $stati_richiesti = ['in_attesa', 'in_revisione', 'approvata', 'completata', 'sospesa', 'respinta'];
        $stati_mancanti = array_diff($stati_richiesti, $stati_attuali);
        
        if (!empty($stati_mancanti)) {
            $problems[] = 'stati_pratiche_incompleti';
            echo "<p>❌ <strong>Problema:</strong> Stati pratiche incompleti - Mancano: " . implode(', ', $stati_mancanti) . "</p>";
        } else {
            echo "<p>✅ <strong>Stati pratiche:</strong> Completi</p>";
        }
    } catch (Exception $e) {
        $problems[] = 'errore_stati_pratiche';
        echo "<p>❌ <strong>Errore verifica stati:</strong> " . $e->getMessage() . "</p>";
    }
    
    // 1.2 Verifica tabelle notifiche
    try {
        $stmt = $db->query("SHOW TABLES LIKE 'notifiche'");
        if ($stmt->rowCount() == 0) {
            $problems[] = 'tabella_notifiche_mancante';
            echo "<p>❌ <strong>Problema:</strong> Tabella notifiche mancante</p>";
        } else {
            echo "<p>✅ <strong>Tabella notifiche:</strong> Presente</p>";
        }
    } catch (Exception $e) {
        $problems[] = 'errore_tabella_notifiche';
        echo "<p>❌ <strong>Errore verifica notifiche:</strong> " . $e->getMessage() . "</p>";
    }
    
    try {
        $stmt = $db->query("SHOW TABLES LIKE 'notifiche_preferenze'");
        if ($stmt->rowCount() == 0) {
            $problems[] = 'tabella_preferenze_mancante';
            echo "<p>❌ <strong>Problema:</strong> Tabella notifiche_preferenze mancante</p>";
        } else {
            echo "<p>✅ <strong>Tabella notifiche_preferenze:</strong> Presente</p>";
        }
    } catch (Exception $e) {
        $problems[] = 'errore_tabella_preferenze';
        echo "<p>❌ <strong>Errore verifica preferenze:</strong> " . $e->getMessage() . "</p>";
    }
    
    // 1.3 Verifica indici
    try {
        $stmt = $db->query("SHOW INDEX FROM pratiche WHERE Key_name LIKE 'idx_%'");
        $indici_performance = $stmt->fetchAll(PDO::FETCH_ASSOC);
        if (count($indici_performance) < 6) {
            $problems[] = 'indici_performance_mancanti';
            echo "<p>❌ <strong>Problema:</strong> Indici performance insufficienti (" . count($indici_performance) . "/6)</p>";
        } else {
            echo "<p>✅ <strong>Indici performance:</strong> Sufficienti</p>";
        }
    } catch (Exception $e) {
        $problems[] = 'errore_indici';
        echo "<p>❌ <strong>Errore verifica indici:</strong> " . $e->getMessage() . "</p>";
    }
    
    if (empty($problems)) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h3>✅ Nessun Problema Trovato!</h3>";
        echo "<p>Il database è configurato correttamente. Tutte le funzionalità dovrebbero essere operative.</p>";
        echo "</div>";
        echo "<p><a href='test_database_update.php'>🧪 Esegui Test Completo</a></p>";
        echo "<p><a href='index.php'>← Torna all'applicazione</a></p>";
        exit;
    }
    
    echo "<h2>🔧 FASE 2: Applicazione Correzioni</h2>";
    
    // Inizia transazione per sicurezza
    $db->beginTransaction();
    
    try {
        // 2.1 Correzione stati pratiche
        if (in_array('stati_pratiche_incompleti', $problems)) {
            echo "<h3>2.1 Correzione Stati Pratiche</h3>";
            try {
                $sql = "ALTER TABLE `pratiche` 
                        MODIFY COLUMN `stato` ENUM(
                            'in_attesa',
                            'in_revisione', 
                            'approvata',
                            'completata',
                            'sospesa',
                            'respinta'
                        ) DEFAULT 'in_attesa'";
                
                $db->exec($sql);
                echo "<p>✅ <strong>Stati pratiche aggiornati</strong> - 6 stati disponibili</p>";
                $fixes_applied[] = "Stati pratiche aggiornati a 6 stati";
                
            } catch (Exception $e) {
                echo "<p>❌ <strong>Errore aggiornamento stati:</strong> " . $e->getMessage() . "</p>";
                $errors_encountered[] = "Errore stati pratiche: " . $e->getMessage();
            }
        }
        
        // 2.2 Creazione tabella notifiche
        if (in_array('tabella_notifiche_mancante', $problems)) {
            echo "<h3>2.2 Creazione Tabella Notifiche</h3>";
            try {
                $sql_notifiche = "CREATE TABLE IF NOT EXISTS `notifiche` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `user_id` int(11) NOT NULL,
                  `tipo` enum('scadenza','pratica','progetto','sistema','fattura','cliente','documento') NOT NULL,
                  `titolo` varchar(255) NOT NULL,
                  `messaggio` text NOT NULL,
                  `priorita` enum('bassa','media','alta') DEFAULT 'media',
                  `link_azione` varchar(255) DEFAULT NULL,
                  `metadata` json DEFAULT NULL,
                  `letta` boolean DEFAULT FALSE,
                  `data_creazione` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  `data_lettura` timestamp NULL DEFAULT NULL,
                  PRIMARY KEY (`id`),
                  KEY `idx_user_letta` (`user_id`, `letta`),
                  KEY `idx_data_creazione` (`data_creazione`),
                  KEY `idx_tipo` (`tipo`),
                  KEY `idx_priorita` (`priorita`),
                  CONSTRAINT `fk_notifiche_user` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
                
                $db->exec($sql_notifiche);
                echo "<p>✅ <strong>Tabella notifiche creata</strong></p>";
                $fixes_applied[] = "Tabella notifiche creata";
                
            } catch (Exception $e) {
                echo "<p>❌ <strong>Errore creazione tabella notifiche:</strong> " . $e->getMessage() . "</p>";
                $errors_encountered[] = "Errore tabella notifiche: " . $e->getMessage();
            }
        }
        
        // 2.3 Creazione tabella preferenze
        if (in_array('tabella_preferenze_mancante', $problems)) {
            echo "<h3>2.3 Creazione Tabella Preferenze</h3>";
            try {
                $sql_preferenze = "CREATE TABLE IF NOT EXISTS `notifiche_preferenze` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `user_id` int(11) NOT NULL,
                  `tipo_notifica` varchar(50) NOT NULL,
                  `email_enabled` boolean DEFAULT TRUE,
                  `push_enabled` boolean DEFAULT TRUE,
                  `soglia_giorni` int(11) DEFAULT 7,
                  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                  PRIMARY KEY (`id`),
                  UNIQUE KEY `unique_user_tipo` (`user_id`, `tipo_notifica`),
                  KEY `idx_user_id` (`user_id`),
                  CONSTRAINT `fk_notifiche_preferenze_user` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
                
                $db->exec($sql_preferenze);
                echo "<p>✅ <strong>Tabella notifiche_preferenze creata</strong></p>";
                $fixes_applied[] = "Tabella notifiche_preferenze creata";
                
            } catch (Exception $e) {
                echo "<p>❌ <strong>Errore creazione tabella preferenze:</strong> " . $e->getMessage() . "</p>";
                $errors_encountered[] = "Errore tabella preferenze: " . $e->getMessage();
            }
        }
        
        // 2.4 Creazione indici performance
        if (in_array('indici_performance_mancanti', $problems)) {
            echo "<h3>2.4 Creazione Indici Performance</h3>";
            $indici = [
                "CREATE INDEX IF NOT EXISTS idx_pratiche_stato ON pratiche (stato)",
                "CREATE INDEX IF NOT EXISTS idx_pratiche_data_scadenza ON pratiche (data_scadenza)",
                "CREATE INDEX IF NOT EXISTS idx_pratiche_data_scadenza_integrazione ON pratiche (data_scadenza_integrazione)",
                "CREATE INDEX IF NOT EXISTS idx_pratiche_stato_data ON pratiche (stato, data_apertura)",
                "CREATE INDEX IF NOT EXISTS idx_pratiche_ente ON pratiche (ente_riferimento)",
                "CREATE INDEX IF NOT EXISTS idx_pratiche_responsabile ON pratiche (responsabile)"
            ];
            
            $indici_creati = 0;
            foreach ($indici as $sql_indice) {
                try {
                    $db->exec($sql_indice);
                    $indici_creati++;
                } catch (Exception $e) {
                    echo "<p>⚠️ <strong>Avviso indice:</strong> " . $e->getMessage() . "</p>";
                }
            }
            
            echo "<p>✅ <strong>Indici performance creati:</strong> {$indici_creati}/6</p>";
            $fixes_applied[] = "Indici performance creati: {$indici_creati}";
        }
        
        echo "<h2>📊 FASE 3: Configurazione Dati Iniziali</h2>";
        
        // 3.1 Preferenze default utenti
        echo "<h3>3.1 Configurazione Preferenze Utenti</h3>";
        try {
            $sql_preferenze_default = "INSERT IGNORE INTO `notifiche_preferenze` (`user_id`, `tipo_notifica`, `email_enabled`, `push_enabled`, `soglia_giorni`)
            SELECT 
                u.id,
                tipo.tipo_notifica,
                TRUE,
                TRUE,
                CASE 
                    WHEN tipo.tipo_notifica IN ('scadenza', 'pratica') THEN 7
                    WHEN tipo.tipo_notifica = 'sistema' THEN 1
                    ELSE 3
                END
            FROM `users` u
            CROSS JOIN (
                SELECT 'scadenza' as tipo_notifica
                UNION SELECT 'pratica'
                UNION SELECT 'progetto' 
                UNION SELECT 'sistema'
                UNION SELECT 'fattura'
                UNION SELECT 'cliente'
                UNION SELECT 'documento'
            ) tipo";
            
            $db->exec($sql_preferenze_default);
            
            $stmt = $db->query("SELECT COUNT(*) as count FROM notifiche_preferenze");
            $count = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "<p>✅ <strong>Preferenze configurate:</strong> {$count['count']} record</p>";
            $fixes_applied[] = "Preferenze utenti configurate: {$count['count']} record";
            
        } catch (Exception $e) {
            echo "<p>❌ <strong>Errore configurazione preferenze:</strong> " . $e->getMessage() . "</p>";
            $errors_encountered[] = "Errore preferenze: " . $e->getMessage();
        }
        
        // 3.2 Notifiche sistema iniziali
        echo "<h3>3.2 Notifiche Sistema Iniziali</h3>";
        try {
            $sql_notifiche_sistema = "INSERT IGNORE INTO `notifiche` (`user_id`, `tipo`, `titolo`, `messaggio`, `priorita`, `metadata`)
            VALUES 
            (1, 'sistema', 'Database corretto', 'Il database è stato corretto e tutte le funzionalità sono ora operative.', 'media', '{\"tipo_notifica\": \"database_corretto\", \"timestamp\": \"" . date('Y-m-d H:i:s') . "\"}'),
            (1, 'sistema', 'Workflow pratiche operativo', 'Il sistema di workflow automatizzato per le pratiche è ora completamente funzionante.', 'bassa', '{\"tipo_notifica\": \"workflow_operativo\"}'),
            (1, 'sistema', 'Sistema notifiche attivo', 'Il sistema di notifiche è stato configurato e attivato con successo.', 'bassa', '{\"tipo_notifica\": \"notifiche_attive\"}')";
            
            $db->exec($sql_notifiche_sistema);
            echo "<p>✅ <strong>Notifiche sistema create</strong></p>";
            $fixes_applied[] = "Notifiche sistema iniziali create";
            
        } catch (Exception $e) {
            echo "<p>❌ <strong>Errore notifiche sistema:</strong> " . $e->getMessage() . "</p>";
            $errors_encountered[] = "Errore notifiche sistema: " . $e->getMessage();
        }
        
        // Commit transazione
        $db->commit();
        echo "<p>✅ <strong>Tutte le correzioni applicate con successo</strong></p>";
        
    } catch (Exception $e) {
        $db->rollback();
        echo "<p>❌ <strong>Errore durante le correzioni - Rollback eseguito:</strong> " . $e->getMessage() . "</p>";
        $errors_encountered[] = "Errore generale: " . $e->getMessage();
    }
    
    echo "<h2>📋 RIEPILOGO CORREZIONI</h2>";
    
    echo "<h3>✅ Correzioni Applicate:</h3>";
    if (empty($fixes_applied)) {
        echo "<p>Nessuna correzione necessaria</p>";
    } else {
        echo "<ul>";
        foreach ($fixes_applied as $fix) {
            echo "<li>✅ {$fix}</li>";
        }
        echo "</ul>";
    }
    
    echo "<h3>❌ Errori Riscontrati:</h3>";
    if (empty($errors_encountered)) {
        echo "<p>✅ Nessun errore durante le correzioni</p>";
    } else {
        echo "<ul>";
        foreach ($errors_encountered as $error) {
            echo "<li>❌ {$error}</li>";
        }
        echo "</ul>";
    }
    
    if (empty($errors_encountered)) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h3>🎉 CORREZIONI COMPLETATE CON SUCCESSO!</h3>";
        echo "<p><strong>Il database è stato corretto e tutte le funzionalità sono ora operative.</strong></p>";
        echo "<ul>";
        echo "<li>✅ Workflow pratiche con 6 stati funzionante</li>";
        echo "<li>✅ Sistema notifiche completo e operativo</li>";
        echo "<li>✅ Performance ottimizzate con indici</li>";
        echo "<li>✅ Preferenze utenti configurate</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<h3>🧪 Prossimi Passi:</h3>";
        echo "<ol>";
        echo "<li><a href='test_database_update.php' target='_blank'>Esegui Test Completo</a></li>";
        echo "<li><a href='test_pratiche.php' target='_blank'>Test PraticheModel</a></li>";
        echo "<li><a href='test_notifiche.php' target='_blank'>Test Sistema Notifiche</a></li>";
        echo "<li><a href='pratiche' target='_blank'>Dashboard Pratiche</a></li>";
        echo "<li><a href='notifiche' target='_blank'>Dashboard Notifiche</a></li>";
        echo "</ol>";
        
    } else {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h3>⚠️ CORREZIONI PARZIALI</h3>";
        echo "<p>Alcune correzioni sono state applicate ma ci sono stati degli errori. Controlla i dettagli sopra.</p>";
        echo "<p><a href='database_diagnosis.php'>🔍 Esegui Nuova Diagnosi</a></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Errore Generale Durante le Correzioni</h2>";
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
    echo "<p><strong>Errore:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . " <strong>Linea:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='index.php'>← Torna all'applicazione</a></p>";
echo "<p><em>Correzioni completate il " . date('Y-m-d H:i:s') . "</em></p>";
?>
