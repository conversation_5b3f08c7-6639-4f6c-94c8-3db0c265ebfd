<?php
// Configurazione del sistema di logging
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', dirname(__FILE__) . '/errors.log');

// <PERSON>nti per i livelli di log
define('LOG_LEVEL_FATAL', 'FATAL ERROR');
define('LOG_LEVEL_ERROR', 'ERROR');
define('LOG_LEVEL_WARNING', 'WARNING');
define('LOG_LEVEL_NOTICE', 'NOTICE');
define('LOG_LEVEL_INFO', 'INFO');
define('LOG_LEVEL_QUERY', 'QUERY');

// Funzione per ottenere l'utente corrente
function getCurrentUser() {
    return isset($_SESSION['user']['username']) ? $_SESSION['user']['username'] : 'Guest';
}

// Funzione per ottenere l'IP dell'utente
function getUserIP() {
    return $_SERVER['REMOTE_ADDR'] ?? 'Unknown IP';
}

// Funzione per formattare il livello di gravità
function formatSeverity($severity) {
    return match($severity) {
        LOG_LEVEL_FATAL   => '🔴 FATAL',
        LOG_LEVEL_ERROR   => '🟠 ERROR',
        LOG_LEVEL_WARNING => '🟡 WARN ',
        LOG_LEVEL_NOTICE  => '🔵 NOTE ',
        LOG_LEVEL_QUERY   => '🟣 QUERY',
        default           => '⚪ INFO '
    };
}

// Funzione per il logging personalizzato
function logError($message, $severity = LOG_LEVEL_ERROR) {
    $timestamp = date('Y-m-d H:i:s');
    $user = getCurrentUser();
    $ip = getUserIP();
    $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1)[0];
    $file = basename($backtrace['file']);
    $line = $backtrace['line'];
    $formattedSeverity = formatSeverity($severity);
    
    $logMessage = sprintf(
        "[%s][%s][%s@%s][%s:%d] %s\n",
        $timestamp,
        $formattedSeverity,
        $user,
        $ip,
        $file,
        $line,
        $message
    );
    
    error_log($logMessage, 3, dirname(__FILE__) . '/errors.log');
}

// Funzione per il logging delle query
function logQuery($query, $params = []) {
    $timestamp = date('Y-m-d H:i:s');
    $user = getCurrentUser();
    $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1)[0];
    $file = basename($backtrace['file']);
    $line = $backtrace['line'];
    
    $paramsStr = !empty($params) ? ' Parametri: ' . json_encode($params, JSON_UNESCAPED_UNICODE) : '';
    $logMessage = sprintf(
        "[%s][%s][%s][%s:%d] Query: %s%s\n",
        $timestamp,
        formatSeverity(LOG_LEVEL_QUERY),
        $user,
        $file,
        $line,
        $query,
        $paramsStr
    );
    
    error_log($logMessage, 3, dirname(__FILE__) . '/queries.log');
}

// Handler personalizzato per gli errori
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    $severity = match($errno) {
        E_ERROR => LOG_LEVEL_FATAL,
        E_WARNING => LOG_LEVEL_WARNING,
        E_NOTICE => LOG_LEVEL_NOTICE,
        default => LOG_LEVEL_ERROR
    };
    
    $message = "$errstr";
    logError($message, $severity);
    return false;
});

// Handler per le eccezioni non catturate
set_exception_handler(function($e) {
    $message = sprintf(
        "Eccezione non catturata: %s\nStack trace:\n%s",
        $e->getMessage(),
        $e->getTraceAsString()
    );
    logError($message, LOG_LEVEL_FATAL);
});
