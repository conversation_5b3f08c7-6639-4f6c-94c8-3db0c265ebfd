<?php

require_once __DIR__ . '/../config/database.php';

try {
    // Leggi gli script SQL
    $createTableSQL = file_get_contents(__DIR__ . '/users.sql');
    $createAdminSQL = file_get_contents(__DIR__ . '/create_admin.sql');
    
    // Esegui gli script
    $db->exec($createTableSQL);
    echo "✅ Tabella users creata con successo\n";
    
    $db->exec($createAdminSQL);
    echo "✅ Utente admin creato con successo\n";
    
    echo "\nCredenziali di accesso:\n";
    echo "Username: admin\n";
    echo "Password: Admin@2024!\n";
    
} catch (PDOException $e) {
    echo "❌ Errore durante l'esecuzione degli script: " . $e->getMessage() . "\n";
}
