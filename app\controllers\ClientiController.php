<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Models\ClientiModel;
use App\Core\Security;

class ClientiController extends Controller {
    private $clientiModel;

    public function __construct() {
        parent::__construct();
        $this->clientiModel = new ClientiModel();
    }

    public function index() {
        try {
            $clienti = $this->clientiModel->getAllClienti();
            $this->view('clienti/index', ['clienti' => $clienti]);
        } catch (\Exception $e) {
            error_log("Errore nel recupero dei clienti (Controller): " . $e->getMessage());
            $this->view('clienti/index', [
                'clienti' => [],
                'error' => 'Si è verificato un errore nel recupero dei clienti.'
            ]);
        }
    }

    public function nuovo() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            if (!Security::validateCsrfToken($_POST['_token'] ?? '')) {
                error_log("CSRF token validation failed for new client creation.");
                $_SESSION['errore'] = 'Si è verificato un problema con la richiesta. Riprova.';
                $this->redirect('clienti/nuovo');
                return;
            }

            $dataToSave = Security::sanitize($_POST);

            if (isset($dataToSave['tipo_cliente'])) {
                if ($dataToSave['tipo_cliente'] === 'azienda') {
                    $dataToSave['nome'] = !empty($dataToSave['nome']) ? $dataToSave['nome'] : null;
                    $dataToSave['cognome'] = !empty($dataToSave['cognome']) ? $dataToSave['cognome'] : null;
                    $dataToSave['codice_fiscale'] = !empty($dataToSave['codice_fiscale']) ? $dataToSave['codice_fiscale'] : null;
                } elseif ($dataToSave['tipo_cliente'] === 'privato') {
                    $dataToSave['ragione_sociale'] = !empty($dataToSave['ragione_sociale']) ? $dataToSave['ragione_sociale'] : null;
                    $dataToSave['partita_iva'] = !empty($dataToSave['partita_iva']) ? $dataToSave['partita_iva'] : null;
                }
            }

            $errori = $this->validaDatiCliente($dataToSave);

            if (empty($errori)) {
                try {
                    $params = [
                        'tipo_cliente' => $dataToSave['tipo_cliente'],
                        'email' => $dataToSave['email'] ?? null,
                        'telefono' => $dataToSave['telefono'] ?? null,
                        'indirizzo' => $dataToSave['indirizzo'] ?? null,
                        'citta' => $dataToSave['citta'] ?? null,
                        'provincia' => $dataToSave['provincia'] ?? null,
                        'note' => $dataToSave['note'] ?? null,
                        'cap_cliente' => $dataToSave['cap_cliente'] ?? null, 
                        'nome' => $dataToSave['nome'] ?? null, 
                        'cognome' => $dataToSave['cognome'] ?? null, 
                        'codice_fiscale' => $dataToSave['codice_fiscale'] ?? null, 
                        'ragione_sociale' => $dataToSave['ragione_sociale'] ?? null, 
                        'partita_iva' => $dataToSave['partita_iva'] ?? null, 
                    ];

                    if (!empty($dataToSave['email'])) {
                        $existingClient = $this->clientiModel->getClientByEmail($dataToSave['email']);
                        if ($existingClient) {
                            $errori[] = "Questo indirizzo email è già utilizzato da un altro cliente.";
                        }
                    }

                    if (empty($errori)) {
                        if ($this->clientiModel->insertCliente($params)) {
                            $_SESSION['messaggio'] = 'Cliente creato con successo.';
                            $this->redirect('clienti');
                            return;
                        } else {
                            $errori['generale'] = "Errore durante il salvataggio del cliente nel database.";
                            error_log("Errore salvataggio cliente (Controller): Fallito insertCliente nel model.");
                        }
                    }
                } catch (\Exception $e) {
                    error_log("Errore creazione cliente (Controller): " . $e->getMessage());
                    $errori['generale'] = "Si è verificato un errore imprevisto durante la creazione del cliente.";
                }
            }

            $this->view('clienti/nuovo', ['errori' => $errori, 'old' => $dataToSave, 'csrf_token' => Security::generateCsrfToken()]);
            return;
        }
        $this->view('clienti/nuovo', ['old' => [], 'errori' => [], 'csrf_token' => Security::generateCsrfToken()]);
    }

    public function modifica($id) {
        $id = filter_var($id, FILTER_VALIDATE_INT);
        if ($id === false) {
            error_log("Tentativo di modifica cliente con ID non valido: " . print_r($id, true));
            $_SESSION['errore'] = 'ID cliente non valido.';
            $this->redirect('clienti');
            return;
        }

        $cliente = $this->clientiModel->getClienteById($id);

        if (!$cliente) {
            $_SESSION['errore'] = 'Cliente non trovato.';
            $this->redirect('clienti');
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            if (!Security::validateCsrfToken($_POST['_token'] ?? '')) {
                error_log("CSRF token validation failed for client update ID: " . $id);
                $_SESSION['errore'] = 'Si è verificato un problema con la richiesta. Riprova.';
                $this->redirect('clienti/modifica/' . $id);
                return;
            }

            $dataToSave = Security::sanitize($_POST);
            $dataToSave['id'] = $id;

            if (isset($dataToSave['tipo_cliente'])) {
                if ($dataToSave['tipo_cliente'] === 'azienda') {
                    $dataToSave['nome'] = !empty($dataToSave['nome']) ? $dataToSave['nome'] : null;
                    $dataToSave['cognome'] = !empty($dataToSave['cognome']) ? $dataToSave['cognome'] : null;
                    $dataToSave['codice_fiscale'] = !empty($dataToSave['codice_fiscale']) ? $dataToSave['codice_fiscale'] : null;
                } elseif ($dataToSave['tipo_cliente'] === 'privato') {
                    $dataToSave['ragione_sociale'] = !empty($dataToSave['ragione_sociale']) ? $dataToSave['ragione_sociale'] : null;
                    $dataToSave['partita_iva'] = !empty($dataToSave['partita_iva']) ? $dataToSave['partita_iva'] : null;
                }
            }

            $errori = $this->validaDatiCliente($dataToSave, true);

            if (empty($errori)) {
                try {
                    $params = [
                        'id' => $dataToSave['id'],
                        'tipo_cliente' => $dataToSave['tipo_cliente'],
                        'email' => $dataToSave['email'] ?? null,
                        'telefono' => $dataToSave['telefono'] ?? null,
                        'indirizzo' => $dataToSave['indirizzo'] ?? null,
                        'citta' => $dataToSave['citta'] ?? null,
                        'provincia' => $dataToSave['provincia'] ?? null,
                        'note' => $dataToSave['note'] ?? null,
                        'cap_cliente' => $dataToSave['cap_cliente'] ?? null,
                        'nome' => $dataToSave['nome'] ?? null,
                        'cognome' => $dataToSave['cognome'] ?? null,
                        'codice_fiscale' => $dataToSave['codice_fiscale'] ?? null,
                        'ragione_sociale' => $dataToSave['ragione_sociale'] ?? null,
                        'partita_iva' => $dataToSave['partita_iva'] ?? null,
                    ];

                    if (!empty($dataToSave['email'])) {
                        $existingClient = $this->clientiModel->getClientByEmail($dataToSave['email']);
                        if ($existingClient && $existingClient['id'] != $id) { 
                            $errori[] = "Questo indirizzo email è già utilizzato da un altro cliente.";
                        }
                    }

                    if (empty($errori)) {
                        if ($this->clientiModel->updateCliente($params)) {
                            $_SESSION['messaggio'] = 'Cliente aggiornato con successo.';
                            $this->redirect('clienti');
                            return;
                        } else {
                            $errori['generale'] = "Errore durante l'aggiornamento del cliente nel database.";
                            error_log("Errore aggiornamento cliente (Controller): Fallito updateCliente nel model per ID: " . $id);
                        }
                    }
                } catch (\Exception $e) {
                    error_log("Errore aggiornamento cliente (Controller) ID $id: " . $e->getMessage());
                    $errori['generale'] = "Si è verificato un errore imprevisto durante l'aggiornamento del cliente.";
                }
            }
            
            $this->view('clienti/modifica', ['cliente' => $cliente, 'errori' => $errori, 'old' => $dataToSave, 'csrf_token' => Security::generateCsrfToken()]);
            return;
        }

        $this->view('clienti/modifica', ['cliente' => $cliente, 'errori' => [], 'old' => $cliente, 'csrf_token' => Security::generateCsrfToken()]);
    }

    public function dettagli($params_url) {
        $id = isset($params_url[0]) ? $params_url[0] : null;
        if (!$id) {
            $this->redirect('clienti?error=id_mancante');
            return;
        }

        try {
            $cliente = $this->clientiModel->getClienteById($id);
            if (!$cliente) {
                $this->redirect('clienti?error=cliente_non_trovato');
                return;
            }
            $progetti = $this->clientiModel->getProgettiByClienteId($id);
            $pratiche = $this->clientiModel->getPraticheByClienteId($id);

            $this->view('clienti/dettagli', [
                'cliente' => $cliente,
                'progetti' => $progetti,
                'pratiche' => $pratiche
            ]);

        } catch (\Exception $e) {
            error_log("Errore nel recupero dei dettagli del cliente (Controller): " . $e->getMessage());
            $this->redirect('clienti?error=errore_recupero_dati');
        }
    }

    public function elimina($params_url) {
        $id = isset($params_url[0]) ? $params_url[0] : null;
        if (!$id) {
            $this->redirect('clienti?error=id_mancante');
            return;
        }

        try {
            $numProgetti = $this->clientiModel->countProgettiByClienteId($id);

            if ($numProgetti > 0) {
                $this->redirect('clienti?error=impossibile_eliminare_cliente_con_progetti');
                return;
            }

            if ($this->clientiModel->deleteClienteById($id)) {
                $this->redirect('clienti?success=cliente_eliminato');
            } else {
                $this->redirect('clienti?error=errore_eliminazione');
            }
        } catch (\Exception $e) {
            error_log("Errore nell'eliminazione del cliente (Controller): " . $e->getMessage());
            $this->redirect('clienti?error=errore_eliminazione_imprevisto');
        }
    }

    private function validaDatiCliente($data, $isUpdate = false) {
        $errori = [];
        
        if (empty($data['tipo_cliente'])) {
            $errori[] = "Il tipo cliente è obbligatorio";
        } elseif (!in_array($data['tipo_cliente'], ['privato', 'azienda'])) {
            $errori[] = "Il tipo cliente non è valido";
        }

        if ($data['tipo_cliente'] === 'privato') {
            if (empty($data['nome'])) $errori[] = "Il nome è obbligatorio";
            if (empty($data['cognome'])) $errori[] = "Il cognome è obbligatorio";
            if (empty($data['codice_fiscale'])) $errori[] = "Il codice fiscale è obbligatorio";
        } else {
            if (empty($data['ragione_sociale'])) $errori[] = "La ragione sociale è obbligatoria";
            if (empty($data['partita_iva'])) $errori[] = "La partita IVA è obbligatoria";
        }

        if (empty($data['email'])) {
            $errori[] = "L'email è obbligatoria";
        } elseif (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errori[] = "L'email non è valida";
        }

        if (empty($data['telefono'])) $errori[] = "Il telefono è obbligatorio";
        if (empty($data['indirizzo'])) $errori[] = "L'indirizzo è obbligatorio";
        if (empty($data['citta'])) $errori[] = "La città è obbligatoria";
        if (empty($data['provincia'])) $errori[] = "La provincia è obbligatoria";

        return $errori;
    }
}