<?php
// emergency_database_fix.php - Correzione emergency completa database
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🚨 CORREZIONE EMERGENCY DATABASE</h1>";
echo "<p><strong>Data:</strong> " . date('Y-m-d H:i:s') . "</p>";

$fixes_applied = [];
$errors = [];

try {
    // Connessione diretta al database
    $pdo = new PDO("mysql:host=localhost;dbname=studio_tecnico;charset=utf8mb4", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✅ <strong>Connessione database:</strong> OK</p>";
    
    // Inizia transazione
    $pdo->beginTransaction();
    
    echo "<h2>🔧 FASE 1: Correzione Struttura Database</h2>";
    
    // 1.1 Verifica e correggi tabella pratiche
    echo "<h3>1.1 Correzione Tabella Pratiche</h3>";
    try {
        // Verifica stati attuali
        $stmt = $pdo->query("SHOW COLUMNS FROM pratiche LIKE 'stato'");
        $column = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($column) {
            preg_match_all("/'([^']+)'/", $column['Type'], $matches);
            $stati_attuali = $matches[1];
            echo "<p><strong>Stati attuali:</strong> " . implode(', ', $stati_attuali) . "</p>";
            
            if (count($stati_attuali) < 6) {
                echo "<p>🔧 <strong>Aggiornamento stati pratiche...</strong></p>";
                $sql = "ALTER TABLE `pratiche` 
                        MODIFY COLUMN `stato` ENUM(
                            'in_attesa',
                            'in_revisione', 
                            'approvata',
                            'completata',
                            'sospesa',
                            'respinta'
                        ) DEFAULT 'in_attesa'";
                
                $pdo->exec($sql);
                echo "<p>✅ <strong>Stati pratiche aggiornati a 6 stati</strong></p>";
                $fixes_applied[] = "Stati pratiche aggiornati";
            } else {
                echo "<p>✅ <strong>Stati pratiche già corretti</strong></p>";
            }
        }
    } catch (Exception $e) {
        echo "<p>❌ <strong>Errore stati pratiche:</strong> " . $e->getMessage() . "</p>";
        $errors[] = "Stati pratiche: " . $e->getMessage();
    }
    
    // 1.2 Creazione tabelle notifiche
    echo "<h3>1.2 Creazione Tabelle Notifiche</h3>";
    try {
        // Verifica se esistono
        $stmt = $pdo->query("SHOW TABLES LIKE 'notifiche'");
        if ($stmt->rowCount() == 0) {
            echo "<p>🔧 <strong>Creazione tabella notifiche...</strong></p>";
            $sql = "CREATE TABLE `notifiche` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) NOT NULL,
              `tipo` enum('scadenza','pratica','progetto','sistema','fattura','cliente','documento') NOT NULL,
              `titolo` varchar(255) NOT NULL,
              `messaggio` text NOT NULL,
              `priorita` enum('bassa','media','alta') DEFAULT 'media',
              `link_azione` varchar(255) DEFAULT NULL,
              `metadata` json DEFAULT NULL,
              `letta` boolean DEFAULT FALSE,
              `data_creazione` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `data_lettura` timestamp NULL DEFAULT NULL,
              PRIMARY KEY (`id`),
              KEY `idx_user_letta` (`user_id`, `letta`),
              KEY `idx_data_creazione` (`data_creazione`),
              KEY `idx_tipo` (`tipo`),
              CONSTRAINT `fk_notifiche_user` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
            
            $pdo->exec($sql);
            echo "<p>✅ <strong>Tabella notifiche creata</strong></p>";
            $fixes_applied[] = "Tabella notifiche creata";
        } else {
            echo "<p>✅ <strong>Tabella notifiche già esistente</strong></p>";
        }
        
        // Tabella preferenze
        $stmt = $pdo->query("SHOW TABLES LIKE 'notifiche_preferenze'");
        if ($stmt->rowCount() == 0) {
            echo "<p>🔧 <strong>Creazione tabella notifiche_preferenze...</strong></p>";
            $sql = "CREATE TABLE `notifiche_preferenze` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) NOT NULL,
              `tipo_notifica` varchar(50) NOT NULL,
              `email_enabled` boolean DEFAULT TRUE,
              `push_enabled` boolean DEFAULT TRUE,
              `soglia_giorni` int(11) DEFAULT 7,
              `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              UNIQUE KEY `unique_user_tipo` (`user_id`, `tipo_notifica`),
              CONSTRAINT `fk_notifiche_preferenze_user` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
            
            $pdo->exec($sql);
            echo "<p>✅ <strong>Tabella notifiche_preferenze creata</strong></p>";
            $fixes_applied[] = "Tabella notifiche_preferenze creata";
        } else {
            echo "<p>✅ <strong>Tabella notifiche_preferenze già esistente</strong></p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ <strong>Errore tabelle notifiche:</strong> " . $e->getMessage() . "</p>";
        $errors[] = "Tabelle notifiche: " . $e->getMessage();
    }
    
    echo "<h2>🔧 FASE 2: Verifica e Correzione Utenti</h2>";
    
    // 2.1 Verifica utenti esistenti
    echo "<h3>2.1 Verifica Utenti</h3>";
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $user_count = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p><strong>Utenti nel database:</strong> " . $user_count['count'] . "</p>";
        
        if ($user_count['count'] == 0) {
            echo "<p>🔧 <strong>Creazione utente amministratore...</strong></p>";
            
            $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
            $sql = "INSERT INTO users (username, email, password, ruolo, attivo) 
                    VALUES ('admin', '<EMAIL>', ?, 'admin', 1)";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$admin_password]);
            
            echo "<p>✅ <strong>Utente admin creato</strong></p>";
            echo "<p><strong>Username:</strong> admin</p>";
            echo "<p><strong>Password:</strong> admin123</p>";
            $fixes_applied[] = "Utente amministratore creato";
            
        } else {
            // Verifica utente admin
            $stmt = $pdo->query("SELECT id, username FROM users WHERE username = 'admin' OR ruolo = 'admin' LIMIT 1");
            $admin = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($admin) {
                echo "<p>✅ <strong>Utente admin trovato:</strong> {$admin['username']} (ID: {$admin['id']})</p>";
            } else {
                echo "<p>⚠️ <strong>Nessun utente admin trovato</strong></p>";
                
                // Crea utente admin
                $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
                $sql = "INSERT INTO users (username, email, password, ruolo, attivo) 
                        VALUES ('admin', '<EMAIL>', ?, 'admin', 1)";
                
                $stmt = $pdo->prepare($sql);
                $stmt->execute([$admin_password]);
                
                echo "<p>✅ <strong>Utente admin creato</strong></p>";
                echo "<p><strong>Username:</strong> admin</p>";
                echo "<p><strong>Password:</strong> admin123</p>";
                $fixes_applied[] = "Utente amministratore aggiunto";
            }
        }
        
    } catch (Exception $e) {
        echo "<p>❌ <strong>Errore gestione utenti:</strong> " . $e->getMessage() . "</p>";
        $errors[] = "Gestione utenti: " . $e->getMessage();
    }
    
    echo "<h2>🔧 FASE 3: Indici Performance</h2>";
    
    // 3.1 Creazione indici
    echo "<h3>3.1 Creazione Indici Performance</h3>";
    $indici = [
        "CREATE INDEX IF NOT EXISTS idx_pratiche_stato ON pratiche (stato)",
        "CREATE INDEX IF NOT EXISTS idx_pratiche_data_scadenza ON pratiche (data_scadenza)",
        "CREATE INDEX IF NOT EXISTS idx_pratiche_data_scadenza_integrazione ON pratiche (data_scadenza_integrazione)",
        "CREATE INDEX IF NOT EXISTS idx_pratiche_stato_data ON pratiche (stato, data_apertura)",
        "CREATE INDEX IF NOT EXISTS idx_pratiche_ente ON pratiche (ente_riferimento)",
        "CREATE INDEX IF NOT EXISTS idx_pratiche_responsabile ON pratiche (responsabile)"
    ];
    
    $indici_creati = 0;
    foreach ($indici as $sql_indice) {
        try {
            $pdo->exec($sql_indice);
            $indici_creati++;
        } catch (Exception $e) {
            echo "<p>⚠️ <strong>Avviso indice:</strong> " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<p>✅ <strong>Indici performance:</strong> {$indici_creati}/6 creati</p>";
    if ($indici_creati > 0) {
        $fixes_applied[] = "Indici performance: {$indici_creati} creati";
    }
    
    echo "<h2>🔧 FASE 4: Dati Iniziali</h2>";
    
    // 4.1 Preferenze utenti
    echo "<h3>4.1 Configurazione Preferenze</h3>";
    try {
        $sql = "INSERT IGNORE INTO `notifiche_preferenze` (`user_id`, `tipo_notifica`, `email_enabled`, `push_enabled`, `soglia_giorni`)
        SELECT 
            u.id,
            tipo.tipo_notifica,
            TRUE,
            TRUE,
            7
        FROM `users` u
        CROSS JOIN (
            SELECT 'scadenza' as tipo_notifica
            UNION SELECT 'pratica'
            UNION SELECT 'progetto' 
            UNION SELECT 'sistema'
        ) tipo";
        
        $pdo->exec($sql);
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM notifiche_preferenze");
        $count = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p>✅ <strong>Preferenze configurate:</strong> {$count['count']} record</p>";
        $fixes_applied[] = "Preferenze utenti configurate";
        
    } catch (Exception $e) {
        echo "<p>❌ <strong>Errore preferenze:</strong> " . $e->getMessage() . "</p>";
        $errors[] = "Preferenze: " . $e->getMessage();
    }
    
    // 4.2 Notifiche sistema
    echo "<h3>4.2 Notifiche Sistema</h3>";
    try {
        $sql = "INSERT IGNORE INTO `notifiche` (`user_id`, `tipo`, `titolo`, `messaggio`, `priorita`)
        VALUES 
        (1, 'sistema', 'Sistema ripristinato', 'Il database è stato corretto e il sistema è ora operativo.', 'media'),
        (1, 'sistema', 'Login disponibile', 'È possibile accedere al sistema con le credenziali amministratore.', 'bassa')";
        
        $pdo->exec($sql);
        echo "<p>✅ <strong>Notifiche sistema create</strong></p>";
        $fixes_applied[] = "Notifiche sistema create";
        
    } catch (Exception $e) {
        echo "<p>❌ <strong>Errore notifiche sistema:</strong> " . $e->getMessage() . "</p>";
        $errors[] = "Notifiche sistema: " . $e->getMessage();
    }
    
    // Commit transazione
    $pdo->commit();
    echo "<p>✅ <strong>Tutte le correzioni applicate con successo</strong></p>";
    
    echo "<h2>📋 RIEPILOGO CORREZIONI</h2>";
    
    echo "<h3>✅ Correzioni Applicate:</h3>";
    if (empty($fixes_applied)) {
        echo "<p>Nessuna correzione necessaria</p>";
    } else {
        echo "<ul>";
        foreach ($fixes_applied as $fix) {
            echo "<li>✅ {$fix}</li>";
        }
        echo "</ul>";
    }
    
    echo "<h3>❌ Errori Riscontrati:</h3>";
    if (empty($errors)) {
        echo "<p>✅ Nessun errore</p>";
    } else {
        echo "<ul>";
        foreach ($errors as $error) {
            echo "<li>❌ {$error}</li>";
        }
        echo "</ul>";
    }
    
    if (empty($errors)) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h3>🎉 SISTEMA RIPRISTINATO!</h3>";
        echo "<p><strong>Il database è stato corretto e il sistema dovrebbe ora funzionare.</strong></p>";
        echo "<h4>Credenziali di accesso:</h4>";
        echo "<p><strong>Username:</strong> admin<br>";
        echo "<strong>Password:</strong> admin123</p>";
        echo "<p><a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 Accedi al Sistema</a></p>";
        echo "</div>";
        
        echo "<h3>🧪 Test Raccomandati:</h3>";
        echo "<ol>";
        echo "<li><a href='index.php' target='_blank'>Test Login</a></li>";
        echo "<li><a href='test_database_update.php' target='_blank'>Test Database</a></li>";
        echo "<li><a href='pratiche' target='_blank'>Test Dashboard Pratiche</a></li>";
        echo "<li><a href='emergency_system_check.php' target='_blank'>Controllo Sistema</a></li>";
        echo "</ol>";
    }
    
} catch (Exception $e) {
    if (isset($pdo)) {
        $pdo->rollback();
    }
    echo "<h2>❌ Errore Critico Durante la Correzione</h2>";
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
    echo "<p><strong>Errore:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . " <strong>Linea:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='emergency_system_check.php'>🔍 Esegui Nuovo Controllo Sistema</a></p>";
echo "<p><a href='index.php'>← Torna all'applicazione</a></p>";
echo "<p><em>Correzione completata il " . date('Y-m-d H:i:s') . "</em></p>";
?>
