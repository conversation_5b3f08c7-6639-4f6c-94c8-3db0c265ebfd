<?php
// test_csrf.php - Script di test per la funzionalità CSRF
session_start();

// Carica il bootstrap
require_once __DIR__ . '/bootstrap.php';

use App\Core\Security;

echo "<h1>Test CSRF Protection</h1>";

try {
    echo "<h2>1. Test Generazione Token CSRF</h2>";
    $token1 = Security::generateCSRFToken();
    echo "Token generato: " . substr($token1, 0, 16) . "...<br>";
    
    $token2 = Security::generateCSRFToken();
    echo "Secondo token (dovrebbe essere uguale): " . substr($token2, 0, 16) . "...<br>";
    echo "Tokens uguali: " . ($token1 === $token2 ? "✅ SÌ" : "❌ NO") . "<br><br>";

    echo "<h2>2. Test Validazione Token CSRF</h2>";
    $validationResult = Security::validateCSRFToken($token1);
    echo "Validazione token corretto: " . ($validationResult ? "✅ VALIDO" : "❌ NON VALIDO") . "<br>";
    
    $invalidValidation = Security::validateCSRFToken("token_falso");
    echo "Validazione token falso: " . ($invalidValidation ? "❌ VALIDO" : "✅ NON VALIDO") . "<br><br>";

    echo "<h2>3. Test Campo CSRF HTML</h2>";
    $csrfField = Security::csrfField();
    echo "Campo HTML generato:<br>";
    echo "<code>" . htmlspecialchars($csrfField) . "</code><br>";
    echo "Campo renderizzato: " . $csrfField . "<br><br>";

    echo "<h2>4. Test Sanificazione Input</h2>";
    $testData = [
        'nome' => '<script>alert("xss")</script>Mario',
        'email' => '<EMAIL>',
        'numero' => '123.45',
        'array_test' => [
            'chiave<script>' => 'valore<img src=x onerror=alert(1)>',
            'normale' => 'testo normale'
        ]
    ];
    
    echo "Dati originali:<br>";
    echo "<pre>" . print_r($testData, true) . "</pre>";
    
    $sanitizedData = Security::sanitizeInput($testData);
    echo "Dati sanificati:<br>";
    echo "<pre>" . print_r($sanitizedData, true) . "</pre>";

    echo "<h2>5. Test Validazione File Upload (simulato)</h2>";
    $fakeFile = [
        'name' => 'test.pdf',
        'type' => 'application/pdf',
        'size' => 1024000, // 1MB
        'tmp_name' => '/tmp/fake',
        'error' => UPLOAD_ERR_OK
    ];
    
    echo "File simulato:<br>";
    echo "<pre>" . print_r($fakeFile, true) . "</pre>";
    
    // Nota: questo test fallirà perché il file non esiste realmente
    $fileErrors = Security::validateFileUpload($fakeFile);
    echo "Errori validazione file:<br>";
    if (empty($fileErrors)) {
        echo "✅ Nessun errore<br>";
    } else {
        echo "<ul>";
        foreach ($fileErrors as $error) {
            echo "<li>❌ " . htmlspecialchars($error) . "</li>";
        }
        echo "</ul>";
    }

    echo "<h2>6. Test Generazione Nome File Sicuro</h2>";
    $originalNames = [
        'documento importante.pdf',
        'file<script>.txt',
        'test/file\\name.doc',
        'àèìòù.pdf',
        '../../etc/passwd'
    ];
    
    foreach ($originalNames as $originalName) {
        $safeName = Security::generateSafeFilename($originalName);
        echo "Originale: <code>" . htmlspecialchars($originalName) . "</code><br>";
        echo "Sicuro: <code>" . htmlspecialchars($safeName) . "</code><br><br>";
    }

    echo "<h2>7. Test Metodi di Utilità CSRF</h2>";
    
    // Simula una richiesta POST con token
    $_POST['_token'] = $token1;
    $verifyResult = Security::verifyCSRFToken();
    echo "Verifica token da POST: " . ($verifyResult ? "✅ VALIDO" : "❌ NON VALIDO") . "<br>";
    
    // Test con token errato
    $_POST['_token'] = 'token_sbagliato';
    $verifyResult2 = Security::verifyCSRFToken();
    echo "Verifica token errato da POST: " . ($verifyResult2 ? "❌ VALIDO" : "✅ NON VALIDO") . "<br>";

    echo "<h2>✅ Tutti i test completati!</h2>";
    echo "<p>La protezione CSRF è stata implementata correttamente.</p>";

} catch (Exception $e) {
    echo "<h2>❌ Errore durante i test</h2>";
    echo "<p>Errore: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>File: " . $e->getFile() . " Linea: " . $e->getLine() . "</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>← Torna all'applicazione</a></p>";
?>
