<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once __DIR__ . '/bootstrap.php';
require_once ROOT_PATH . '/app/core/Autoloader.php';

use App\Core\Autoloader;
use App\Config\Database;

// Registra l'autoloader
Autoloader::register();

try {
    $db = Database::getInstance();
    
    // Password da impostare
    $newPassword = 'admin123';  // Questa sarà la nuova password
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    
    // Aggiorna la password dell'admin
    $sql = "UPDATE users SET password = ? WHERE username = 'admin'";
    $stmt = $db->prepare($sql);
    $result = $stmt->execute([$hashedPassword]);
    
    if ($result) {
        echo "Password reimpostata con successo!<br>";
        echo "Username: admin<br>";
        echo "Password: " . $newPassword . "<br>";
        
        // Verifica che la password sia stata salvata correttamente
        $sql = "SELECT password FROM users WHERE username = 'admin'";
        $stmt = $db->query($sql);
        $user = $stmt->fetch();
        
        if ($user && password_verify($newPassword, $user['password'])) {
            echo "Verifica della password riuscita!";
        } else {
            echo "ERRORE: La verifica della password è fallita!";
        }
    } else {
        echo "Errore durante la reimpostazione della password";
    }
    
} catch (Exception $e) {
    echo "<h1>Errore</h1>";
    echo "<pre>";
    echo "Messaggio: " . htmlspecialchars($e->getMessage()) . "\n";
    echo "File: " . htmlspecialchars($e->getFile()) . "\n";
    echo "Linea: " . $e->getLine() . "\n";
    echo "Stack trace:\n" . htmlspecialchars($e->getTraceAsString());
    echo "</pre>";
}
