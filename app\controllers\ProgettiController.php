<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Config\Database;
use PDO;
use PDOException;

class ProgettiController extends Controller {
    private $db;

    public function __construct() {
        try {
            parent::__construct();
            $this->db = Database::getInstance();
            if (!$this->db instanceof PDO) {
                throw new \Exception("Connessione al database non valida");
            }
        } catch (\Exception $e) {
            error_log("Errore nell'inizializzazione del controller: " . $e->getMessage());
            throw $e;
        }
    }

    public function index() {
        try {
            $stmt = $this->db->query("
                SELECT p.*, 
                       c.tipo_cliente,
                       c.nome as cliente_nome,
                       c.cognome as cliente_cognome,
                       c.ragione_sociale as cliente_ragione_sociale,
                       (SELECT COUNT(*) FROM pratiche pr WHERE pr.progetto_id = p.id) as num_pratiche
                FROM progetti p 
                LEFT JOIN clienti c ON p.cliente_id = c.id 
                ORDER BY p.data_inizio DESC
            ");
            
            if ($stmt === false) {
                throw new PDOException("Errore nell'esecuzione della query");
            }
            
            $progetti = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $this->view('progetti/index', ['progetti' => $progetti]);
        } catch (PDOException $e) {
            error_log("Errore nel recupero dei progetti: " . $e->getMessage());
            $this->view('progetti/index', [
                'progetti' => [],
                'error' => 'Si è verificato un errore nel recupero dei progetti.'
            ]);
        }
    }

    public function nuovo() {
        // Recupera lista clienti per il select
        $stmt = $this->db->query("SELECT id, tipo_cliente, nome, cognome, ragione_sociale FROM clienti ORDER BY cognome, nome");
        
        if ($stmt === false) {
            throw new PDOException("Errore nell'esecuzione della query");
        }
        
        $clienti = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($this->isPost()) {
            // Verifica CSRF token
            if (!$this->verificaCsrf()) {
                die('Token CSRF non valido');
            }
            
            // Validazione
            $errori = [];
            if (empty($this->getPost('cliente_id'))) $errori['cliente_id'] = "Il cliente è obbligatorio";
            if (empty($this->getPost('nome_progetto'))) $errori['nome_progetto'] = "Il nome del progetto è obbligatorio";
            if (empty($this->getPost('data_inizio'))) $errori['data_inizio'] = "La data di inizio è obbligatoria";
            if (empty($this->getPost('data_fine_prevista'))) $errori['data_fine_prevista'] = "La data di fine prevista è obbligatoria";

            if (empty($errori)) {
                try {
                    $stmt = $this->db->prepare("INSERT INTO progetti (cliente_id, nome_progetto, descrizione, data_inizio, data_fine_prevista, stato, tipo_progetto, priorita, importo, comune, indirizzo_progetto) VALUES (:cliente_id, :nome_progetto, :descrizione, :data_inizio, :data_fine_prevista, :stato, :tipo_progetto, :priorita, :importo, :comune, :indirizzo_progetto)");
                    
                    // Assegna i valori POST a variabili
                    $cliente_id = $this->getPost('cliente_id');
                    $nome_progetto = $this->getPost('nome_progetto');
                    $descrizione = $this->getPost('descrizione');
                    $data_inizio = $this->getPost('data_inizio');
                    $data_fine_prevista = $this->getPost('data_fine_prevista');
                    $stato = $this->getPost('stato');
                    $tipo_progetto = $this->getPost('tipo_progetto');
                    $priorita = $this->getPost('priorita');
                    $importo_raw = $this->getPost('importo');
                    $importo = !empty($importo_raw) ? $importo_raw : null;
                    $comune = $this->getPost('comune');
                    $indirizzo_progetto = $this->getPost('indirizzo_progetto');

                    // Usa le variabili con bindParam
                    $stmt->bindParam(':cliente_id', $cliente_id);
                    $stmt->bindParam(':nome_progetto', $nome_progetto);
                    $stmt->bindParam(':descrizione', $descrizione);
                    $stmt->bindParam(':data_inizio', $data_inizio);
                    $stmt->bindParam(':data_fine_prevista', $data_fine_prevista);
                    $stmt->bindParam(':stato', $stato);
                    $stmt->bindParam(':tipo_progetto', $tipo_progetto);
                    $stmt->bindParam(':priorita', $priorita);
                    $stmt->bindParam(':importo', $importo);
                    $stmt->bindParam(':comune', $comune);
                    $stmt->bindParam(':indirizzo_progetto', $indirizzo_progetto);
                    
                    if (!$stmt->execute()) {
                        throw new PDOException("Errore durante l'inserimento");
                    }
                    
                    $this->redirect('progetti');
                } catch (PDOException $e) {
                    $errori[] = "Errore durante il salvataggio: " . $e->getMessage();
                }
            }
        }

        $this->view('progetti/nuovo', [
            'clienti' => $clienti,
            'errori' => $errori ?? [],
            'old' => $_POST ?? []
        ]);
    }

    public function modifica($params) {
        try {
            // Gestisce sia array associativo che numerico
            $id = is_array($params) ? (isset($params['id']) ? $params['id'] : $params[0]) : $params;
            
            if (!$id) {
                $this->redirect('progetti?error=progetto_non_trovato');
            }

            // Recupera i dati del progetto
            $stmt = $this->db->prepare("SELECT * FROM progetti WHERE id = :id");
            $stmt->bindParam(':id', $id);
            
            if (!$stmt->execute()) {
                throw new PDOException("Errore durante il recupero del progetto");
            }
            
            $progetto = $stmt->fetch(PDO::FETCH_ASSOC);
            if (!$progetto) {
                $this->redirect('progetti?error=progetto_non_trovato');
            }

            // Recupera lista clienti per il select
            $stmt = $this->db->query("
                SELECT c.*, 
                       CASE 
                           WHEN c.tipo_cliente = 'privato' THEN CONCAT(c.nome, ' ', c.cognome)
                           ELSE c.ragione_sociale 
                       END as nome_cliente
                FROM clienti c 
                ORDER BY nome_cliente
            ");
            
            if ($stmt === false) {
                throw new PDOException("Errore nell'esecuzione della query");
            }
            
            $clienti = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if ($this->isPost()) {
                // Verifica CSRF token
                if (!$this->verificaCsrf()) {
                    throw new \Exception('Token CSRF non valido');
                }
                
                // Validazione
                $errori = [];
                if (empty($this->getPost('nome_progetto'))) $errori['nome_progetto'] = "Il nome del progetto è obbligatorio";
                if (empty($this->getPost('cliente_id'))) $errori['cliente_id'] = "Il cliente è obbligatorio";
                if (empty($this->getPost('tipo_progetto'))) $errori['tipo_progetto'] = "Il tipo progetto è obbligatorio";
                if (empty($this->getPost('data_inizio'))) $errori['data_inizio'] = "La data di inizio è obbligatoria";
                if (empty($this->getPost('indirizzo'))) $errori['indirizzo'] = "L'indirizzo è obbligatorio";
                if (empty($this->getPost('cap'))) $errori['cap'] = "Il CAP è obbligatorio";
                if (empty($this->getPost('citta'))) $errori['citta'] = "La città è obbligatoria";
                if (empty($this->getPost('provincia'))) $errori['provincia'] = "La provincia è obbligatoria";

                // Validazione CAP
                if (!empty($this->getPost('cap')) && !preg_match('/^[0-9]{5}$/', $this->getPost('cap'))) {
                    $errori['cap'] = "Il CAP deve essere composto da 5 cifre";
                }

                // Validazione provincia
                if (!empty($this->getPost('provincia')) && !preg_match('/^[A-Z]{2}$/', strtoupper($this->getPost('provincia')))) {
                    $errori['provincia'] = "La provincia deve essere composta da 2 lettere";
                }

                if (empty($errori)) {
                    try {
                        $stmt = $this->db->prepare("
                            UPDATE progetti SET
                                nome_progetto = :nome_progetto,
                                cliente_id = :cliente_id,
                                tipo_progetto = :tipo_progetto,
                                stato_progetto = :stato_progetto,
                                data_inizio = :data_inizio,
                                data_fine_prevista = :data_fine_prevista,
                                indirizzo = :indirizzo,
                                cap = :cap,
                                citta = :citta,
                                provincia = :provincia,
                                descrizione = :descrizione,
                                note = :note
                            WHERE id = :id
                        ");
                        
                        // Assegna i valori POST a variabili
                        $nome_progetto = $this->getPost('nome_progetto');
                        $cliente_id = $this->getPost('cliente_id');
                        $tipo_progetto = $this->getPost('tipo_progetto');
                        $stato_progetto = $this->getPost('stato_progetto');
                        $data_inizio = $this->getPost('data_inizio');
                        $data_fine_prevista = $this->getPost('data_fine_prevista');
                        $indirizzo = $this->getPost('indirizzo');
                        $cap = $this->getPost('cap');
                        $citta = $this->getPost('citta');
                        $provincia = strtoupper($this->getPost('provincia'));
                        $descrizione = $this->getPost('descrizione');
                        $note = $this->getPost('note');

                        // Usa le variabili con bindParam
                        $stmt->bindParam(':nome_progetto', $nome_progetto);
                        $stmt->bindParam(':cliente_id', $cliente_id);
                        $stmt->bindParam(':tipo_progetto', $tipo_progetto);
                        $stmt->bindParam(':stato_progetto', $stato_progetto);
                        $stmt->bindParam(':data_inizio', $data_inizio);
                        $stmt->bindParam(':data_fine_prevista', $data_fine_prevista);
                        $stmt->bindParam(':indirizzo', $indirizzo);
                        $stmt->bindParam(':cap', $cap);
                        $stmt->bindParam(':citta', $citta);
                        $stmt->bindParam(':provincia', $provincia);
                        $stmt->bindParam(':descrizione', $descrizione);
                        $stmt->bindParam(':note', $note);
                        $stmt->bindParam(':id', $id);
                        
                        if (!$stmt->execute()) {
                            throw new PDOException("Errore durante l'aggiornamento del progetto");
                        }
                        
                        $this->redirect('progetti');
                    } catch (PDOException $e) {
                        error_log("Errore durante l'aggiornamento: " . $e->getMessage());
                        $errori['database'] = "Errore durante l'aggiornamento del progetto";
                    }
                }
            }

            $this->view('progetti/modifica', [
                'progetto' => $progetto,
                'clienti' => $clienti,
                'errors' => $errori ?? [],
                'old' => $_POST ?? []
            ]);
        } catch (\Exception $e) {
            error_log("Errore nella pagina modifica: " . $e->getMessage());
            throw $e;
        }
    }

    public function elimina($params) {
        try {
            $id = isset($params[0]) ? $params[0] : null;
            
            if (!$id) {
                $this->redirect('progetti?error=progetto_non_trovato');
            }

            // Controlla se esistono pratiche collegate
            $stmt = $this->db->prepare("SELECT COUNT(*) as num_pratiche FROM pratiche WHERE progetto_id = :id");
            $stmt->execute(['id' => $id]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($result['num_pratiche'] > 0) {
                $this->redirect('progetti?error=impossibile_eliminare_progetto_con_pratiche');
                return;
            }

            // Se non ci sono pratiche collegate, procedi con l'eliminazione
            $stmt = $this->db->prepare("DELETE FROM progetti WHERE id = :id");
            if (!$stmt->execute(['id' => $id])) {
                throw new PDOException("Errore durante l'eliminazione del progetto");
            }

            $this->redirect('progetti?success=progetto_eliminato');
        } catch (PDOException $e) {
            error_log("Errore nell'eliminazione del progetto: " . $e->getMessage());
            $this->redirect('progetti?error=errore_eliminazione');
        }
    }

    public function dettagli($params) {
        $id = isset($params[0]) ? intval($params[0]) : null;
        $isModal = isset($_GET['modal']) && $_GET['modal'] === 'true';
        
        if (!$id) {
            if ($isModal) {
                echo "Progetto non trovato";
                exit;
            }
            header('Location: ' . BASE_URL . 'progetti?error=progetto_non_trovato');
            exit;
        }

        try {
            $query = "SELECT 
                        p.*,
                        c.nome as cliente_nome,
                        c.cognome as cliente_cognome,
                        c.ragione_sociale as cliente_ragione_sociale,
                        c.tipo_cliente
                    FROM progetti p
                    JOIN clienti c ON p.cliente_id = c.id
                    WHERE p.id = :id";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            $progetto = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$progetto) {
                if ($isModal) {
                    echo "Progetto non trovato";
                    exit;
                }
                header('Location: ' . BASE_URL . 'progetti?error=progetto_non_trovato');
                exit;
            }

            // Se è una richiesta modal, carica una vista semplificata
            if ($isModal) {
                $this->view('progetti/dettagli_modal', ['progetto' => $progetto]);
                exit;
            }

            // Recupera le pratiche collegate al progetto
            $queryPratiche = "
                SELECT 
                    p.*,
                    pr.nome_progetto,
                    COALESCE(p.tipo_pratica, p.tipo_documento) as tipo_pratica,
                    CASE 
                        WHEN c.tipo_cliente = 'privato' THEN CONCAT(c.nome, ' ', c.cognome)
                        ELSE c.ragione_sociale 
                    END as cliente_nome
                FROM pratiche p
                JOIN progetti pr ON p.progetto_id = pr.id
                JOIN clienti c ON pr.cliente_id = c.id
                WHERE p.progetto_id = :progetto_id
                ORDER BY p.data_apertura DESC";
            
            $stmtPratiche = $this->db->prepare($queryPratiche);
            $stmtPratiche->bindParam(':progetto_id', $id);
            $stmtPratiche->execute();
            $pratiche = $stmtPratiche->fetchAll(PDO::FETCH_ASSOC);

            $this->view('progetti/dettagli', [
                'progetto' => $progetto,
                'pratiche' => $pratiche
            ]);
        } catch (PDOException $e) {
            error_log("Errore nel recupero dei dettagli del progetto: " . $e->getMessage());
            if ($isModal) {
                echo "Errore nel caricamento dei dettagli";
                exit;
            }
            header('Location: ' . BASE_URL . 'progetti?error=errore_recupero_dati');
            exit;
        }
    }
}
