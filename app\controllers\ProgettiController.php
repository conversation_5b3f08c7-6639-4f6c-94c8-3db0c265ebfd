<?php
// app/controllers/ProgettiController.php - Controller per la gestione progetti
namespace App\Controllers;

use App\Core\Controller;
use App\Config\Database;
use App\Models\ProgettiModel;
use App\Models\ClientiModel;
use PDO;
use PDOException;

class ProgettiController extends Controller {
    private PDO $db;
    private ProgettiModel $progettiModel;
    private ClientiModel $clientiModel;

    public function __construct() {
        try {
            parent::__construct();
            $this->db = Database::getInstance();
            if (!$this->db instanceof PDO) {
                throw new \Exception("Connessione al database non valida");
            }
            $this->progettiModel = new ProgettiModel();
            $this->clientiModel = new ClientiModel();
        } catch (\Exception $e) {
            error_log("Errore nell'inizializzazione del ProgettiController: " . $e->getMessage());
            throw $e;
        }
    }

    public function index() {
        try {
            // Recupera filtri dalla query string se presenti
            $filters = [];
            if (!empty($_GET['stato'])) {
                $filters['stato'] = $_GET['stato'];
            }
            if (!empty($_GET['cliente_id'])) {
                $filters['cliente_id'] = (int)$_GET['cliente_id'];
            }
            if (!empty($_GET['tipo_progetto'])) {
                $filters['tipo_progetto'] = $_GET['tipo_progetto'];
            }

            $progetti = $this->progettiModel->getAllProgetti($filters);
            $this->view('progetti/index', ['progetti' => $progetti]);
        } catch (\Exception $e) {
            error_log("Errore nel recupero dei progetti: " . $e->getMessage());
            $this->view('progetti/index', [
                'progetti' => [],
                'error' => 'Si è verificato un errore nel recupero dei progetti.'
            ]);
        }
    }

    public function nuovo() {
        try {
            // Recupera lista clienti per il select usando il ClientiModel
            $clienti = $this->clientiModel->getAllClienti();

            if ($this->isPost()) {
                // Verifica CSRF token
                if (!$this->verificaCsrf()) {
                    die('Token CSRF non valido');
                }

                // Validazione
                $errori = [];
                if (empty($this->getPost('cliente_id'))) $errori['cliente_id'] = "Il cliente è obbligatorio";
                if (empty($this->getPost('nome_progetto'))) $errori['nome_progetto'] = "Il nome del progetto è obbligatorio";
                if (empty($this->getPost('data_inizio'))) $errori['data_inizio'] = "La data di inizio è obbligatoria";
                if (empty($this->getPost('data_fine_prevista'))) $errori['data_fine_prevista'] = "La data di fine prevista è obbligatoria";

                if (empty($errori)) {
                    try {
                        // Prepara i dati per il Model
                        $data = [
                            'cliente_id' => $this->getPost('cliente_id'),
                            'nome_progetto' => $this->getPost('nome_progetto'),
                            'descrizione' => $this->getPost('descrizione'),
                            'data_inizio' => $this->getPost('data_inizio'),
                            'data_fine_prevista' => $this->getPost('data_fine_prevista'),
                            'stato' => $this->getPost('stato') ?: 'in_corso',
                            'tipo_progetto' => $this->getPost('tipo_progetto') ?: 'altro',
                            'priorita' => $this->getPost('priorita') ?: 'media',
                            'importo' => $this->getPost('importo'),
                            'comune' => $this->getPost('comune'),
                            'indirizzo_progetto' => $this->getPost('indirizzo_progetto')
                        ];

                        if ($this->progettiModel->insertProgetto($data)) {
                            $this->redirect('progetti');
                        } else {
                            $errori[] = "Errore durante il salvataggio del progetto";
                        }
                    } catch (\Exception $e) {
                        error_log("Errore in ProgettiController::nuovo(): " . $e->getMessage());
                        $errori[] = "Errore durante il salvataggio: " . $e->getMessage();
                    }
                }
            }

            $this->view('progetti/nuovo', [
                'clienti' => $clienti,
                'errori' => $errori ?? [],
                'old' => $_POST ?? []
            ]);
        } catch (\Exception $e) {
            error_log("Errore in ProgettiController::nuovo(): " . $e->getMessage());
            $this->view('progetti/nuovo', [
                'clienti' => [],
                'errori' => ['Errore nel caricamento della pagina'],
                'old' => $_POST ?? []
            ]);
        }
    }

    public function modifica($params) {
        try {
            // Gestisce sia array associativo che numerico
            $id = is_array($params) ? (isset($params['id']) ? $params['id'] : $params[0]) : $params;

            if (!$id) {
                $this->redirect('progetti?error=progetto_non_trovato');
                return;
            }

            // Recupera i dati del progetto usando il Model
            $progetto = $this->progettiModel->getProgettoById((int)$id);
            if (!$progetto) {
                $this->redirect('progetti?error=progetto_non_trovato');
                return;
            }

            // Recupera lista clienti per il select usando il ClientiModel
            $clienti = $this->clientiModel->getAllClienti();

            if ($this->isPost()) {
                // Verifica CSRF token
                if (!$this->verificaCsrf()) {
                    throw new \Exception('Token CSRF non valido');
                }

                // Validazione
                $errori = [];
                if (empty($this->getPost('nome_progetto'))) $errori['nome_progetto'] = "Il nome del progetto è obbligatorio";
                if (empty($this->getPost('cliente_id'))) $errori['cliente_id'] = "Il cliente è obbligatorio";
                if (empty($this->getPost('data_inizio'))) $errori['data_inizio'] = "La data di inizio è obbligatoria";
                if (empty($this->getPost('data_fine_prevista'))) $errori['data_fine_prevista'] = "La data di fine prevista è obbligatoria";

                if (empty($errori)) {
                    try {
                        // Prepara i dati per il Model
                        $data = [
                            'id' => $id,
                            'cliente_id' => $this->getPost('cliente_id'),
                            'nome_progetto' => $this->getPost('nome_progetto'),
                            'descrizione' => $this->getPost('descrizione'),
                            'data_inizio' => $this->getPost('data_inizio'),
                            'data_fine_prevista' => $this->getPost('data_fine_prevista'),
                            'stato' => $this->getPost('stato') ?: 'in_corso',
                            'tipo_progetto' => $this->getPost('tipo_progetto') ?: 'altro',
                            'priorita' => $this->getPost('priorita') ?: 'media',
                            'importo' => $this->getPost('importo'),
                            'comune' => $this->getPost('comune'),
                            'indirizzo_progetto' => $this->getPost('indirizzo_progetto')
                        ];

                        if ($this->progettiModel->updateProgetto($data)) {
                            $this->redirect('progetti');
                        } else {
                            $errori['database'] = "Errore durante l'aggiornamento del progetto";
                        }
                    } catch (\Exception $e) {
                        error_log("Errore in ProgettiController::modifica(): " . $e->getMessage());
                        $errori['database'] = "Errore durante l'aggiornamento del progetto";
                    }
                }
            }

            $this->view('progetti/modifica', [
                'progetto' => $progetto,
                'clienti' => $clienti,
                'errors' => $errori ?? [],
                'old' => $_POST ?? []
            ]);
        } catch (\Exception $e) {
            error_log("Errore nella pagina modifica: " . $e->getMessage());
            throw $e;
        }
    }

    public function elimina($params) {
        try {
            $id = isset($params[0]) ? (int)$params[0] : null;

            if (!$id) {
                $this->redirect('progetti?error=progetto_non_trovato');
                return;
            }

            // Verifica se il progetto può essere eliminato usando il Model
            if (!$this->progettiModel->canDeleteProgetto($id)) {
                $this->redirect('progetti?error=impossibile_eliminare_progetto_con_pratiche');
                return;
            }

            // Procedi con l'eliminazione usando il Model
            if ($this->progettiModel->deleteProgetto($id)) {
                $this->redirect('progetti?success=progetto_eliminato');
            } else {
                $this->redirect('progetti?error=errore_eliminazione');
            }
        } catch (\Exception $e) {
            error_log("Errore in ProgettiController::elimina(): " . $e->getMessage());
            $this->redirect('progetti?error=errore_eliminazione');
        }
    }

    public function dettagli($params) {
        $id = isset($params[0]) ? intval($params[0]) : null;
        $isModal = isset($_GET['modal']) && $_GET['modal'] === 'true';

        if (!$id) {
            if ($isModal) {
                echo "Progetto non trovato";
                exit;
            }
            header('Location: ' . BASE_URL . 'progetti?error=progetto_non_trovato');
            exit;
        }

        try {
            // Recupera i dati del progetto usando il Model
            $progetto = $this->progettiModel->getProgettoById($id);

            if (!$progetto) {
                if ($isModal) {
                    echo "Progetto non trovato";
                    exit;
                }
                header('Location: ' . BASE_URL . 'progetti?error=progetto_non_trovato');
                exit;
            }

            // Se è una richiesta modal, carica una vista semplificata
            if ($isModal) {
                $this->view('progetti/dettagli_modal', ['progetto' => $progetto]);
                exit;
            }

            // Recupera le pratiche collegate al progetto (temporaneo, sarà migliorato con PraticheModel)
            $queryPratiche = "
                SELECT
                    p.*,
                    pr.nome_progetto,
                    COALESCE(p.tipo_pratica, p.tipo_documento) as tipo_pratica,
                    CASE
                        WHEN c.tipo_cliente = 'privato' THEN CONCAT(c.nome, ' ', c.cognome)
                        ELSE c.ragione_sociale
                    END as cliente_nome
                FROM pratiche p
                JOIN progetti pr ON p.progetto_id = pr.id
                JOIN clienti c ON pr.cliente_id = c.id
                WHERE p.progetto_id = :progetto_id
                ORDER BY p.data_apertura DESC";

            $stmtPratiche = $this->db->prepare($queryPratiche);
            $stmtPratiche->bindParam(':progetto_id', $id);
            $stmtPratiche->execute();
            $pratiche = $stmtPratiche->fetchAll(PDO::FETCH_ASSOC);

            $this->view('progetti/dettagli', [
                'progetto' => $progetto,
                'pratiche' => $pratiche
            ]);
        } catch (\Exception $e) {
            error_log("Errore in ProgettiController::dettagli(): " . $e->getMessage());
            if ($isModal) {
                echo "Errore nel caricamento dei dettagli";
                exit;
            }
            header('Location: ' . BASE_URL . 'progetti?error=errore_recupero_dati');
            exit;
        }
    }
}
