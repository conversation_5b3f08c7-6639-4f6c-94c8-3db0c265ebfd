<?php
// emergency_system_check.php - Controllo urgente sistema e log errori
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🚨 CONTROLLO URGENTE SISTEMA STUDIO TECNICO</h1>";
echo "<p><strong>Data Controllo:</strong> " . date('Y-m-d H:i:s') . "</p>";

$critical_errors = [];
$warnings = [];
$system_status = [];

// Controllo 1: Connessione Database
echo "<h2>🔍 CONTROLLO 1: Connessione Database</h2>";
try {
    // Test connessione diretta
    $host = 'localhost';
    $dbname = 'studio_tecnico';
    $username = 'root';
    $password = '';
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✅ <strong>Connessione Database:</strong> OK</p>";
    $system_status['database_connection'] = true;
    
    // Verifica versione
    $stmt = $pdo->query("SELECT VERSION() as version");
    $version = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p>✅ <strong>Versione MySQL:</strong> " . $version['version'] . "</p>";
    
} catch (Exception $e) {
    echo "<p>❌ <strong>ERRORE CONNESSIONE DATABASE:</strong> " . $e->getMessage() . "</p>";
    $critical_errors[] = "Database non raggiungibile: " . $e->getMessage();
    $system_status['database_connection'] = false;
}

// Controllo 2: Struttura Database
if ($system_status['database_connection']) {
    echo "<h2>🔍 CONTROLLO 2: Struttura Database</h2>";
    
    // Verifica tabelle esistenti
    try {
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "<p><strong>Tabelle trovate:</strong> " . count($tables) . "</p>";
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>{$table}</li>";
        }
        echo "</ul>";
        
        // Verifica tabelle critiche
        $required_tables = ['users', 'pratiche', 'progetti', 'clienti'];
        $missing_tables = array_diff($required_tables, $tables);
        
        if (empty($missing_tables)) {
            echo "<p>✅ <strong>Tabelle principali:</strong> Tutte presenti</p>";
        } else {
            echo "<p>❌ <strong>Tabelle mancanti:</strong> " . implode(', ', $missing_tables) . "</p>";
            $critical_errors[] = "Tabelle mancanti: " . implode(', ', $missing_tables);
        }
        
        // Verifica tabelle notifiche
        $notifiche_tables = ['notifiche', 'notifiche_preferenze'];
        $notifiche_missing = array_diff($notifiche_tables, $tables);
        
        if (empty($notifiche_missing)) {
            echo "<p>✅ <strong>Tabelle notifiche:</strong> Presenti</p>";
        } else {
            echo "<p>❌ <strong>Tabelle notifiche mancanti:</strong> " . implode(', ', $notifiche_missing) . "</p>";
            $critical_errors[] = "Tabelle notifiche mancanti: " . implode(', ', $notifiche_missing);
        }
        
    } catch (Exception $e) {
        echo "<p>❌ <strong>ERRORE VERIFICA TABELLE:</strong> " . $e->getMessage() . "</p>";
        $critical_errors[] = "Errore verifica tabelle: " . $e->getMessage();
    }
    
    // Verifica struttura tabella pratiche
    echo "<h3>Verifica Tabella Pratiche</h3>";
    try {
        $stmt = $pdo->query("DESCRIBE pratiche");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p><strong>Colonne tabella pratiche:</strong></p>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Campo</th><th>Tipo</th><th>Null</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Verifica colonna stato
        $stato_column = null;
        foreach ($columns as $column) {
            if ($column['Field'] === 'stato') {
                $stato_column = $column;
                break;
            }
        }
        
        if ($stato_column) {
            echo "<p><strong>Colonna stato:</strong> " . $stato_column['Type'] . "</p>";
            
            // Estrai stati dall'enum
            preg_match_all("/'([^']+)'/", $stato_column['Type'], $matches);
            $stati_attuali = $matches[1];
            echo "<p><strong>Stati disponibili:</strong> " . implode(', ', $stati_attuali) . " (" . count($stati_attuali) . " totali)</p>";
            
            if (count($stati_attuali) < 6) {
                $critical_errors[] = "Stati pratiche incompleti: solo " . count($stati_attuali) . " invece di 6";
            }
        } else {
            $critical_errors[] = "Colonna stato non trovata nella tabella pratiche";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ <strong>ERRORE VERIFICA PRATICHE:</strong> " . $e->getMessage() . "</p>";
        $critical_errors[] = "Errore verifica pratiche: " . $e->getMessage();
    }
    
    // Verifica dati utenti
    echo "<h3>Verifica Dati Utenti</h3>";
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $user_count = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p><strong>Utenti nel database:</strong> " . $user_count['count'] . "</p>";
        
        if ($user_count['count'] == 0) {
            $critical_errors[] = "Nessun utente nel database - impossibile fare login";
        } else {
            // Mostra primi utenti
            $stmt = $pdo->query("SELECT id, username, email FROM users LIMIT 3");
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<p><strong>Primi utenti:</strong></p>";
            echo "<ul>";
            foreach ($users as $user) {
                echo "<li>ID: {$user['id']}, Username: {$user['username']}, Email: {$user['email']}</li>";
            }
            echo "</ul>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ <strong>ERRORE VERIFICA UTENTI:</strong> " . $e->getMessage() . "</p>";
        $critical_errors[] = "Errore verifica utenti: " . $e->getMessage();
    }
}

// Controllo 3: File di Configurazione
echo "<h2>🔍 CONTROLLO 3: File di Configurazione</h2>";

// Verifica bootstrap.php
if (file_exists(__DIR__ . '/bootstrap.php')) {
    echo "<p>✅ <strong>bootstrap.php:</strong> Presente</p>";
    
    // Prova a includere bootstrap
    try {
        ob_start();
        include_once __DIR__ . '/bootstrap.php';
        $bootstrap_output = ob_get_clean();
        
        if (!empty($bootstrap_output)) {
            echo "<p>⚠️ <strong>Output bootstrap:</strong> " . htmlspecialchars($bootstrap_output) . "</p>";
            $warnings[] = "Bootstrap produce output: " . $bootstrap_output;
        } else {
            echo "<p>✅ <strong>Bootstrap caricamento:</strong> OK</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ <strong>ERRORE BOOTSTRAP:</strong> " . $e->getMessage() . "</p>";
        $critical_errors[] = "Errore caricamento bootstrap: " . $e->getMessage();
    }
} else {
    echo "<p>❌ <strong>bootstrap.php:</strong> MANCANTE</p>";
    $critical_errors[] = "File bootstrap.php mancante";
}

// Verifica file di configurazione database
$config_files = [
    'app/config/Database.php',
    'app/config/database.php',
    'config/database.php'
];

$config_found = false;
foreach ($config_files as $config_file) {
    if (file_exists(__DIR__ . '/' . $config_file)) {
        echo "<p>✅ <strong>Config Database:</strong> {$config_file} presente</p>";
        $config_found = true;
        break;
    }
}

if (!$config_found) {
    echo "<p>❌ <strong>Config Database:</strong> Nessun file di configurazione trovato</p>";
    $critical_errors[] = "File configurazione database mancante";
}

// Controllo 4: Classi e Autoloader
echo "<h2>🔍 CONTROLLO 4: Classi e Autoloader</h2>";

// Test caricamento classi principali
$classes_to_test = [
    'App\\Config\\Database',
    'App\\Models\\PraticheModel',
    'App\\Services\\NotificationService',
    'App\\Controllers\\PraticheController'
];

foreach ($classes_to_test as $class) {
    try {
        if (class_exists($class)) {
            echo "<p>✅ <strong>Classe {$class}:</strong> Caricabile</p>";
        } else {
            echo "<p>❌ <strong>Classe {$class}:</strong> NON TROVATA</p>";
            $critical_errors[] = "Classe {$class} non trovata";
        }
    } catch (Exception $e) {
        echo "<p>❌ <strong>Errore classe {$class}:</strong> " . $e->getMessage() . "</p>";
        $critical_errors[] = "Errore caricamento {$class}: " . $e->getMessage();
    }
}

// Controllo 5: Test Login
echo "<h2>🔍 CONTROLLO 5: Test Processo Login</h2>";

if ($system_status['database_connection']) {
    try {
        // Simula processo login
        $stmt = $pdo->prepare("SELECT id, username, password FROM users WHERE username = ? LIMIT 1");
        $stmt->execute(['admin']); // Prova con username admin
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            echo "<p>✅ <strong>Utente admin trovato:</strong> ID {$user['id']}</p>";
            echo "<p><strong>Password hash:</strong> " . substr($user['password'], 0, 20) . "...</p>";
            
            // Verifica se la password è hashata
            if (strlen($user['password']) == 60 && substr($user['password'], 0, 4) == '$2y$') {
                echo "<p>✅ <strong>Password:</strong> Correttamente hashata (bcrypt)</p>";
            } else {
                echo "<p>⚠️ <strong>Password:</strong> Potrebbe non essere hashata correttamente</p>";
                $warnings[] = "Password utente admin potrebbe non essere hashata";
            }
        } else {
            echo "<p>❌ <strong>Utente admin:</strong> NON TROVATO</p>";
            $critical_errors[] = "Utente admin non trovato nel database";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ <strong>ERRORE TEST LOGIN:</strong> " . $e->getMessage() . "</p>";
        $critical_errors[] = "Errore test login: " . $e->getMessage();
    }
}

// Controllo 6: Log Errori PHP
echo "<h2>🔍 CONTROLLO 6: Log Errori PHP</h2>";

// Verifica configurazione log
$log_errors = ini_get('log_errors');
$error_log = ini_get('error_log');

echo "<p><strong>Log errori abilitato:</strong> " . ($log_errors ? 'Sì' : 'No') . "</p>";
echo "<p><strong>File log errori:</strong> " . ($error_log ?: 'Default') . "</p>";

// Cerca file di log comuni
$possible_logs = [
    'C:/xampp/php/logs/php_error_log',
    'C:/xampp/apache/logs/error.log',
    'error_log',
    'php_errors.log'
];

foreach ($possible_logs as $log_file) {
    if (file_exists($log_file)) {
        echo "<p>✅ <strong>Log trovato:</strong> {$log_file}</p>";
        
        // Leggi ultime righe del log
        $log_content = file_get_contents($log_file);
        $log_lines = explode("\n", $log_content);
        $recent_lines = array_slice($log_lines, -10); // Ultime 10 righe
        
        echo "<p><strong>Ultime righe del log:</strong></p>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; max-height: 200px; overflow-y: auto;'>";
        foreach ($recent_lines as $line) {
            if (!empty(trim($line))) {
                echo htmlspecialchars($line) . "\n";
            }
        }
        echo "</pre>";
        break;
    }
}

// RIEPILOGO FINALE
echo "<h2>📋 RIEPILOGO CONTROLLO SISTEMA</h2>";

echo "<h3>🚨 Errori Critici:</h3>";
if (empty($critical_errors)) {
    echo "<p>✅ <strong>Nessun errore critico trovato</strong></p>";
} else {
    echo "<ul style='color: red;'>";
    foreach ($critical_errors as $error) {
        echo "<li>❌ {$error}</li>";
    }
    echo "</ul>";
}

echo "<h3>⚠️ Avvisi:</h3>";
if (empty($warnings)) {
    echo "<p>✅ <strong>Nessun avviso</strong></p>";
} else {
    echo "<ul style='color: orange;'>";
    foreach ($warnings as $warning) {
        echo "<li>⚠️ {$warning}</li>";
    }
    echo "</ul>";
}

// Raccomandazioni immediate
echo "<h3>🔧 Azioni Immediate Raccomandate:</h3>";
echo "<ol>";

if (!empty($critical_errors)) {
    if (in_array("Nessun utente nel database - impossibile fare login", $critical_errors)) {
        echo "<li><strong>URGENTE:</strong> <a href='create_admin_user.php'>Crea utente amministratore</a></li>";
    }
    
    if (count(array_filter($critical_errors, function($e) { return strpos($e, 'Stati pratiche') !== false; })) > 0) {
        echo "<li><strong>URGENTE:</strong> <a href='fix_pratiche_states.php'>Correggi stati pratiche</a></li>";
    }
    
    if (count(array_filter($critical_errors, function($e) { return strpos($e, 'Tabelle notifiche') !== false; })) > 0) {
        echo "<li><strong>URGENTE:</strong> <a href='create_notifiche_tables.php'>Crea tabelle notifiche</a></li>";
    }
    
    echo "<li><strong>Esegui correzione completa:</strong> <a href='emergency_database_fix.php'>Correzione Emergency Database</a></li>";
} else {
    echo "<li>✅ Sistema sembra funzionante - Verifica manuale login</li>";
}

echo "</ol>";

echo "<hr>";
echo "<p><a href='index.php'>← Torna all'applicazione</a></p>";
echo "<p><em>Controllo completato il " . date('Y-m-d H:i:s') . "</em></p>";
?>
