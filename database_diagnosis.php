<?php
// database_diagnosis.php - Diagnosi completa errori database Studio Tecnico
session_start();

// Carica il bootstrap
require_once __DIR__ . '/bootstrap.php';

use App\Config\Database;

echo "<h1>🔍 Diagnosi Completa Database Studio Tecnico</h1>";
echo "<p><strong>Data Diagnosi:</strong> " . date('Y-m-d H:i:s') . "</p>";

$issues_found = [];
$critical_issues = [];

try {
    $db = Database::getInstance();
    
    echo "<h2>📋 FASE 1: Verifica Struttura Database</h2>";
    
    // 1.1 Verifica versione database
    echo "<h3>1.1 Informazioni Database</h3>";
    $stmt = $db->query("SELECT VERSION() as version");
    $version = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p>✅ <strong>Versione Database:</strong> " . $version['version'] . "</p>";
    
    // 1.2 Verifica tabella pratiche e stati
    echo "<h3>1.2 Verifica Tabella Pratiche</h3>";
    try {
        $stmt = $db->query("DESCRIBE pratiche");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $stato_column = null;
        foreach ($columns as $column) {
            if ($column['Field'] === 'stato') {
                $stato_column = $column;
                break;
            }
        }
        
        if ($stato_column) {
            echo "<p>✅ <strong>Colonna stato trovata:</strong> " . $stato_column['Type'] . "</p>";
            
            // Estrai gli stati dall'enum
            preg_match_all("/'([^']+)'/", $stato_column['Type'], $matches);
            $stati_attuali = $matches[1];
            $stati_richiesti = ['in_attesa', 'in_revisione', 'approvata', 'completata', 'sospesa', 'respinta'];
            
            echo "<p><strong>Stati attuali:</strong> " . implode(', ', $stati_attuali) . "</p>";
            echo "<p><strong>Stati richiesti:</strong> " . implode(', ', $stati_richiesti) . "</p>";
            
            $stati_mancanti = array_diff($stati_richiesti, $stati_attuali);
            if (empty($stati_mancanti)) {
                echo "<p>✅ <strong>Stati pratiche:</strong> Tutti i 6 stati sono presenti</p>";
            } else {
                echo "<p>❌ <strong>Stati mancanti:</strong> " . implode(', ', $stati_mancanti) . "</p>";
                $critical_issues[] = "Stati pratiche incompleti: mancano " . implode(', ', $stati_mancanti);
            }
        } else {
            echo "<p>❌ <strong>Colonna stato non trovata!</strong></p>";
            $critical_issues[] = "Colonna stato non trovata nella tabella pratiche";
        }
        
        // Verifica dati esistenti
        $stmt = $db->query("SELECT stato, COUNT(*) as count FROM pratiche GROUP BY stato");
        $stati_dati = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<p><strong>Distribuzione dati per stato:</strong></p>";
        echo "<ul>";
        foreach ($stati_dati as $stato_data) {
            echo "<li>{$stato_data['stato']}: {$stato_data['count']} pratiche</li>";
        }
        echo "</ul>";
        
    } catch (Exception $e) {
        echo "<p>❌ <strong>Errore verifica tabella pratiche:</strong> " . $e->getMessage() . "</p>";
        $critical_issues[] = "Errore accesso tabella pratiche: " . $e->getMessage();
    }
    
    // 1.3 Verifica tabelle notifiche
    echo "<h3>1.3 Verifica Tabelle Notifiche</h3>";
    
    // Verifica tabella notifiche
    try {
        $stmt = $db->query("SHOW TABLES LIKE 'notifiche'");
        $notifiche_exists = $stmt->rowCount() > 0;
        
        if ($notifiche_exists) {
            echo "<p>✅ <strong>Tabella notifiche:</strong> Esiste</p>";
            
            // Verifica struttura
            $stmt = $db->query("DESCRIBE notifiche");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<p><strong>Colonne tabella notifiche:</strong> " . count($columns) . "</p>";
            
            // Verifica dati
            $stmt = $db->query("SELECT COUNT(*) as count FROM notifiche");
            $count = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "<p><strong>Record in notifiche:</strong> " . $count['count'] . "</p>";
            
        } else {
            echo "<p>❌ <strong>Tabella notifiche:</strong> NON ESISTE</p>";
            $critical_issues[] = "Tabella notifiche non esiste";
        }
    } catch (Exception $e) {
        echo "<p>❌ <strong>Errore verifica tabella notifiche:</strong> " . $e->getMessage() . "</p>";
        $issues_found[] = "Errore tabella notifiche: " . $e->getMessage();
    }
    
    // Verifica tabella notifiche_preferenze
    try {
        $stmt = $db->query("SHOW TABLES LIKE 'notifiche_preferenze'");
        $preferenze_exists = $stmt->rowCount() > 0;
        
        if ($preferenze_exists) {
            echo "<p>✅ <strong>Tabella notifiche_preferenze:</strong> Esiste</p>";
            
            // Verifica dati
            $stmt = $db->query("SELECT COUNT(*) as count FROM notifiche_preferenze");
            $count = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "<p><strong>Record in notifiche_preferenze:</strong> " . $count['count'] . "</p>";
            
        } else {
            echo "<p>❌ <strong>Tabella notifiche_preferenze:</strong> NON ESISTE</p>";
            $critical_issues[] = "Tabella notifiche_preferenze non esiste";
        }
    } catch (Exception $e) {
        echo "<p>❌ <strong>Errore verifica tabella preferenze:</strong> " . $e->getMessage() . "</p>";
        $issues_found[] = "Errore tabella preferenze: " . $e->getMessage();
    }
    
    // 1.4 Verifica indici performance
    echo "<h3>1.4 Verifica Indici Performance</h3>";
    try {
        $stmt = $db->query("SHOW INDEX FROM pratiche");
        $indici = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p><strong>Indici totali su pratiche:</strong> " . count($indici) . "</p>";
        
        $indici_performance = [];
        foreach ($indici as $indice) {
            if (strpos($indice['Key_name'], 'idx_') === 0) {
                $indici_performance[] = $indice['Key_name'];
            }
        }
        
        echo "<p><strong>Indici performance (idx_*):</strong> " . count($indici_performance) . "</p>";
        if (!empty($indici_performance)) {
            echo "<ul>";
            foreach (array_unique($indici_performance) as $indice) {
                echo "<li>{$indice}</li>";
            }
            echo "</ul>";
        }
        
        if (count($indici_performance) < 6) {
            $issues_found[] = "Indici performance insufficienti: " . count($indici_performance) . "/6";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ <strong>Errore verifica indici:</strong> " . $e->getMessage() . "</p>";
        $issues_found[] = "Errore verifica indici: " . $e->getMessage();
    }
    
    echo "<h2>🔍 FASE 2: Verifica Foreign Keys e Vincoli</h2>";
    
    // 2.1 Verifica foreign keys
    echo "<h3>2.1 Verifica Foreign Keys</h3>";
    try {
        $stmt = $db->query("
            SELECT 
                TABLE_NAME,
                CONSTRAINT_NAME,
                COLUMN_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND REFERENCED_TABLE_NAME IS NOT NULL
            AND TABLE_NAME IN ('pratiche', 'notifiche', 'notifiche_preferenze')
        ");
        $foreign_keys = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p><strong>Foreign Keys trovate:</strong> " . count($foreign_keys) . "</p>";
        if (!empty($foreign_keys)) {
            echo "<ul>";
            foreach ($foreign_keys as $fk) {
                echo "<li>{$fk['TABLE_NAME']}.{$fk['COLUMN_NAME']} → {$fk['REFERENCED_TABLE_NAME']}.{$fk['REFERENCED_COLUMN_NAME']}</li>";
            }
            echo "</ul>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ <strong>Errore verifica foreign keys:</strong> " . $e->getMessage() . "</p>";
        $issues_found[] = "Errore foreign keys: " . $e->getMessage();
    }
    
    // 2.2 Verifica integrità referenziale
    echo "<h3>2.2 Verifica Integrità Referenziale</h3>";
    try {
        // Verifica che tutti gli user_id nelle notifiche esistano
        $stmt = $db->query("
            SELECT COUNT(*) as invalid_users
            FROM notifiche n
            LEFT JOIN users u ON n.user_id = u.id
            WHERE u.id IS NULL
        ");
        $invalid_users = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($invalid_users['invalid_users'] > 0) {
            echo "<p>❌ <strong>Notifiche con user_id non validi:</strong> " . $invalid_users['invalid_users'] . "</p>";
            $issues_found[] = "Notifiche con user_id non validi: " . $invalid_users['invalid_users'];
        } else {
            echo "<p>✅ <strong>Integrità user_id notifiche:</strong> OK</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>⚠️ <strong>Impossibile verificare integrità:</strong> " . $e->getMessage() . "</p>";
        // Non è un errore critico se le tabelle non esistono ancora
    }
    
    echo "<h2>📊 FASE 3: Test Funzionalità</h2>";
    
    // 3.1 Test PraticheModel
    echo "<h3>3.1 Test PraticheModel</h3>";
    try {
        $praticheModel = new \App\Models\PraticheModel();
        
        // Test metodi base
        $stati_disponibili = $praticheModel->getStatiDisponibili();
        echo "<p>✅ <strong>getStatiDisponibili():</strong> " . count($stati_disponibili) . " stati</p>";
        
        $pratiche = $praticheModel->getAllPratiche();
        echo "<p>✅ <strong>getAllPratiche():</strong> " . count($pratiche) . " pratiche</p>";
        
        // Test workflow
        $can_transition = $praticheModel->canTransition('in_attesa', 'in_revisione');
        echo "<p>✅ <strong>canTransition():</strong> " . ($can_transition ? 'Funziona' : 'Non funziona') . "</p>";
        
    } catch (Exception $e) {
        echo "<p>❌ <strong>Errore PraticheModel:</strong> " . $e->getMessage() . "</p>";
        $critical_issues[] = "PraticheModel non funziona: " . $e->getMessage();
    }
    
    // 3.2 Test NotificationService
    echo "<h3>3.2 Test NotificationService</h3>";
    try {
        $notificationService = new \App\Services\NotificationService();
        
        // Test creazione notifica
        $test_notifica = [
            'user_id' => 1,
            'tipo' => 'sistema',
            'titolo' => 'Test diagnosi',
            'messaggio' => 'Test notifica durante diagnosi',
            'priorita' => 'bassa'
        ];
        
        $result = $notificationService->createNotifica($test_notifica);
        echo "<p>✅ <strong>createNotifica():</strong> " . ($result ? 'Funziona' : 'Non funziona') . "</p>";
        
    } catch (Exception $e) {
        echo "<p>❌ <strong>Errore NotificationService:</strong> " . $e->getMessage() . "</p>";
        $critical_issues[] = "NotificationService non funziona: " . $e->getMessage();
    }
    
    echo "<h2>📋 RIEPILOGO DIAGNOSI</h2>";
    
    echo "<h3>🚨 Problemi Critici Identificati:</h3>";
    if (empty($critical_issues)) {
        echo "<p>✅ <strong>Nessun problema critico trovato</strong></p>";
    } else {
        echo "<ul style='color: red;'>";
        foreach ($critical_issues as $issue) {
            echo "<li>❌ {$issue}</li>";
        }
        echo "</ul>";
    }
    
    echo "<h3>⚠️ Problemi Minori Identificati:</h3>";
    if (empty($issues_found)) {
        echo "<p>✅ <strong>Nessun problema minore trovato</strong></p>";
    } else {
        echo "<ul style='color: orange;'>";
        foreach ($issues_found as $issue) {
            echo "<li>⚠️ {$issue}</li>";
        }
        echo "</ul>";
    }
    
    // Raccomandazioni
    echo "<h3>💡 Raccomandazioni:</h3>";
    if (!empty($critical_issues) || !empty($issues_found)) {
        echo "<ol>";
        
        if (in_array("Tabella notifiche non esiste", $critical_issues)) {
            echo "<li><strong>Creare tabella notifiche</strong> - Eseguire script creazione tabelle</li>";
        }
        
        if (in_array("Tabella notifiche_preferenze non esiste", $critical_issues)) {
            echo "<li><strong>Creare tabella notifiche_preferenze</strong> - Eseguire script creazione tabelle</li>";
        }
        
        if (count(array_filter($critical_issues, function($issue) { return strpos($issue, 'Stati pratiche') !== false; })) > 0) {
            echo "<li><strong>Aggiornare enum stati pratiche</strong> - Eseguire ALTER TABLE per aggiungere stati mancanti</li>";
        }
        
        if (count(array_filter($issues_found, function($issue) { return strpos($issue, 'Indici performance') !== false; })) > 0) {
            echo "<li><strong>Creare indici performance</strong> - Eseguire script creazione indici</li>";
        }
        
        echo "<li><strong>Eseguire script di correzione automatica</strong> - Utilizzare il pulsante qui sotto</li>";
        echo "</ol>";
        
        echo "<div style='margin: 20px 0; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;'>";
        echo "<h4>🔧 Azione Raccomandata:</h4>";
        echo "<p>Eseguire lo script di correzione automatica per risolvere tutti i problemi identificati.</p>";
        echo "<a href='database_fix_executor.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔧 Esegui Correzione Automatica</a>";
        echo "</div>";
        
    } else {
        echo "<p>✅ <strong>Il database è configurato correttamente!</strong> Tutte le funzionalità dovrebbero essere operative.</p>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Errore Generale Durante la Diagnosi</h2>";
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
    echo "<p><strong>Errore:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . " <strong>Linea:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='index.php'>← Torna all'applicazione</a></p>";
echo "<p><em>Diagnosi completata il " . date('Y-m-d H:i:s') . "</em></p>";
?>
