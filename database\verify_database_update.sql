-- database/verify_database_update.sql
-- Script di verifica post-aggiornamento database Studio Tecnico
-- Verifica: Workflow Pratiche + Sistema Notifiche
-- Data: 5 Gennaio 2025

-- =====================================================
-- VERIFICA 1: STRUTTURA TABELLA PRATICHE
-- =====================================================

SELECT 'VERIFICA STRUTTURA TABELLA PRATICHE' as test_name;

-- Verifica colonna stato con nuovi valori enum
SHOW COLUMNS FROM `pratiche` LIKE 'stato';

-- Test inserimento con nuovi stati
INSERT INTO `pratiche` (`progetto_id`, `tipo_documento`, `numero_pratica`, `stato`, `note`) 
VALUES 
(1, 'SCIA', 'TEST-COMPLETATA', 'completata', 'Test stato completata'),
(1, 'CILA', 'TEST-SOSPESA', 'sospesa', 'Test stato sospesa'),
(1, 'PERMESSO', 'TEST-RESPINTA', 'respinta', 'Test stato respinta');

-- Verifica che i nuovi stati siano stati inseriti correttamente
SELECT 'TEST NUOVI STATI' as test_type, stato, COUNT(*) as count
FROM pratiche 
WHERE numero_pratica LIKE 'TEST-%'
GROUP BY stato;

-- =====================================================
-- VERIFICA 2: INDICI PERFORMANCE
-- =====================================================

SELECT 'VERIFICA INDICI PRATICHE' as test_name;

-- Lista tutti gli indici della tabella pratiche
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX,
    NON_UNIQUE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'pratiche'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- Test performance query con indici
EXPLAIN SELECT * FROM pratiche WHERE stato = 'in_attesa';
EXPLAIN SELECT * FROM pratiche WHERE data_scadenza BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY);

-- =====================================================
-- VERIFICA 3: TABELLE NOTIFICHE
-- =====================================================

SELECT 'VERIFICA TABELLE NOTIFICHE' as test_name;

-- Verifica esistenza tabelle
SELECT 
    TABLE_NAME,
    TABLE_ROWS,
    TABLE_COMMENT
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('notifiche', 'notifiche_preferenze');

-- Verifica struttura tabella notifiche
DESCRIBE `notifiche`;

-- Verifica struttura tabella preferenze
DESCRIBE `notifiche_preferenze`;

-- =====================================================
-- VERIFICA 4: FOREIGN KEYS E INTEGRITÀ
-- =====================================================

SELECT 'VERIFICA FOREIGN KEYS' as test_name;

-- Lista tutte le foreign key
SELECT 
    TABLE_NAME,
    CONSTRAINT_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = DATABASE() 
AND REFERENCED_TABLE_NAME IS NOT NULL
AND TABLE_NAME IN ('pratiche', 'notifiche', 'notifiche_preferenze');

-- Test integrità referenziale
SELECT 'TEST INTEGRITÀ REFERENZIALE' as test_type;

-- Verifica che tutte le notifiche abbiano user_id validi
SELECT 
    'notifiche_user_id' as test,
    COUNT(*) as total_notifiche,
    COUNT(u.id) as valid_user_ids,
    COUNT(*) - COUNT(u.id) as invalid_user_ids
FROM notifiche n
LEFT JOIN users u ON n.user_id = u.id;

-- Verifica che tutte le preferenze abbiano user_id validi
SELECT 
    'preferenze_user_id' as test,
    COUNT(*) as total_preferenze,
    COUNT(u.id) as valid_user_ids,
    COUNT(*) - COUNT(u.id) as invalid_user_ids
FROM notifiche_preferenze np
LEFT JOIN users u ON np.user_id = u.id;

-- =====================================================
-- VERIFICA 5: FUNZIONALITÀ AVANZATE
-- =====================================================

SELECT 'VERIFICA FUNZIONALITÀ AVANZATE' as test_name;

-- Test vista statistiche
SELECT 'TEST VISTA STATISTICHE' as test_type;
SELECT * FROM v_notifiche_stats LIMIT 3;

-- Test funzione conteggio notifiche
SELECT 'TEST FUNZIONE CONTEGGIO' as test_type;
SELECT fn_count_notifiche_non_lette(1) as notifiche_non_lette_user_1;

-- Test stored procedure (dry run)
SELECT 'TEST STORED PROCEDURE' as test_type;
CALL sp_pulisci_notifiche_vecchie(365); -- Elimina notifiche più vecchie di 1 anno

-- Verifica trigger
SELECT 'TEST TRIGGER' as test_type;
-- Inserisci una notifica di test
INSERT INTO notifiche (user_id, tipo, titolo, messaggio) 
VALUES (1, 'sistema', 'Test Trigger', 'Test aggiornamento data lettura');

-- Marca come letta e verifica che data_lettura sia aggiornata
SET @test_notifica_id = LAST_INSERT_ID();
UPDATE notifiche SET letta = TRUE WHERE id = @test_notifica_id;

-- Verifica che data_lettura sia stata impostata
SELECT 
    id, 
    letta, 
    data_lettura IS NOT NULL as data_lettura_impostata,
    data_lettura
FROM notifiche 
WHERE id = @test_notifica_id;

-- =====================================================
-- VERIFICA 6: DATI INIZIALI
-- =====================================================

SELECT 'VERIFICA DATI INIZIALI' as test_name;

-- Verifica preferenze utenti
SELECT 'PREFERENZE UTENTI' as test_type;
SELECT 
    u.username,
    COUNT(np.id) as preferenze_configurate
FROM users u
LEFT JOIN notifiche_preferenze np ON u.id = np.user_id
GROUP BY u.id, u.username;

-- Verifica notifiche di sistema iniziali
SELECT 'NOTIFICHE SISTEMA INIZIALI' as test_type;
SELECT 
    titolo,
    messaggio,
    data_creazione
FROM notifiche 
WHERE tipo = 'sistema' 
AND JSON_EXTRACT(metadata, '$.tipo_notifica') IN ('database_aggiornato', 'workflow_attivato', 'notifiche_attivate')
ORDER BY data_creazione;

-- =====================================================
-- VERIFICA 7: WORKFLOW PRATICHE
-- =====================================================

SELECT 'VERIFICA WORKFLOW PRATICHE' as test_name;

-- Test transizioni workflow
SELECT 'TEST TRANSIZIONI WORKFLOW' as test_type;

-- Crea una pratica di test per workflow
INSERT INTO pratiche (progetto_id, tipo_documento, numero_pratica, stato, note)
VALUES (1, 'SCIA', 'TEST-WORKFLOW', 'in_attesa', 'Test workflow completo');

SET @test_pratica_id = LAST_INSERT_ID();

-- Test transizione valida: in_attesa -> in_revisione
UPDATE pratiche SET stato = 'in_revisione' WHERE id = @test_pratica_id;

-- Test transizione valida: in_revisione -> approvata
UPDATE pratiche SET stato = 'approvata' WHERE id = @test_pratica_id;

-- Test transizione valida: approvata -> completata
UPDATE pratiche SET stato = 'completata' WHERE id = @test_pratica_id;

-- Verifica storico transizioni
SELECT 
    'STORICO TRANSIZIONI' as test_type,
    id,
    numero_pratica,
    stato as stato_finale
FROM pratiche 
WHERE id = @test_pratica_id;

-- =====================================================
-- VERIFICA 8: PERFORMANCE E STATISTICHE
-- =====================================================

SELECT 'VERIFICA PERFORMANCE' as test_name;

-- Test query statistiche pratiche
SELECT 'STATISTICHE PRATICHE' as test_type;
SELECT 
    stato,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM pratiche), 2) as percentuale
FROM pratiche 
GROUP BY stato
ORDER BY count DESC;

-- Test query scadenze
SELECT 'PRATICHE IN SCADENZA' as test_type;
SELECT COUNT(*) as pratiche_in_scadenza_7gg
FROM pratiche 
WHERE data_scadenza BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)
AND stato NOT IN ('completata', 'respinta');

-- Test performance notifiche
SELECT 'PERFORMANCE NOTIFICHE' as test_type;
SELECT 
    COUNT(*) as totale_notifiche,
    SUM(CASE WHEN letta = FALSE THEN 1 ELSE 0 END) as non_lette,
    COUNT(DISTINCT user_id) as utenti_con_notifiche
FROM notifiche;

-- =====================================================
-- VERIFICA 9: PULIZIA TEST DATA
-- =====================================================

SELECT 'PULIZIA DATI TEST' as test_name;

-- Rimuovi dati di test creati durante la verifica
DELETE FROM pratiche WHERE numero_pratica LIKE 'TEST-%';
DELETE FROM notifiche WHERE titolo = 'Test Trigger';

-- =====================================================
-- REPORT FINALE VERIFICA
-- =====================================================

SELECT 'REPORT FINALE VERIFICA DATABASE' as report_type;

-- Riepilogo tabelle
SELECT 
    'RIEPILOGO TABELLE' as sezione,
    'pratiche' as tabella,
    COUNT(*) as record_count,
    'Workflow 6 stati attivo' as stato
FROM pratiche
UNION ALL
SELECT 
    'RIEPILOGO TABELLE' as sezione,
    'notifiche' as tabella,
    COUNT(*) as record_count,
    'Sistema notifiche attivo' as stato
FROM notifiche
UNION ALL
SELECT 
    'RIEPILOGO TABELLE' as sezione,
    'notifiche_preferenze' as tabella,
    COUNT(*) as record_count,
    'Preferenze configurate' as stato
FROM notifiche_preferenze;

-- Verifica funzionalità critiche
SELECT 
    'FUNZIONALITÀ CRITICHE' as sezione,
    'Workflow Pratiche' as funzionalita,
    CASE WHEN COUNT(DISTINCT stato) >= 6 THEN 'OK' ELSE 'ERRORE' END as stato
FROM pratiche
UNION ALL
SELECT 
    'FUNZIONALITÀ CRITICHE' as sezione,
    'Sistema Notifiche' as funzionalita,
    CASE WHEN COUNT(*) > 0 THEN 'OK' ELSE 'ERRORE' END as stato
FROM notifiche
UNION ALL
SELECT 
    'FUNZIONALITÀ CRITICHE' as sezione,
    'Preferenze Utenti' as funzionalita,
    CASE WHEN COUNT(*) > 0 THEN 'OK' ELSE 'ERRORE' END as stato
FROM notifiche_preferenze;

SELECT 
    'VERIFICA COMPLETATA CON SUCCESSO!' as messaggio,
    NOW() as timestamp_verifica,
    'Database pronto per PraticheModel e NotificationService' as stato;
