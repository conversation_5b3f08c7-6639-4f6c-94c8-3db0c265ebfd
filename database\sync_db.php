<?php

class DatabaseSync {
    // Configurazione locale
    private $localHost = 'localhost';
    private $localUser = 'root';
    private $localPass = '';
    private $localDb = 'studio_tecnico';
    
    // Configurazione remota (stesse credenziali di XAMPP)
    private $remoteHost = 'localhost';
    private $remoteUser = 'root';
    private $remotePass = '';
    private $remoteDb = 'studio_tecnico';
    
    // Percorso per il backup
    private $backupPath;
    
    public function __construct() {
        $this->backupPath = __DIR__ . '/backup/';
        if (!file_exists($this->backupPath)) {
            mkdir($this->backupPath, 0777, true);
        }
    }
    
    /**
     * Esegue il backup del database locale
     */
    public function backupLocalDatabase() {
        $timestamp = date('Y-m-d_H-i-s');
        $filename = $this->backupPath . "backup_{$timestamp}.sql";
        
        // Comando per il backup
        $command = sprintf(
            'c:/xampp/mysql/bin/mysqldump -h %s -u %s %s %s > %s',
            escapeshellarg($this->localHost),
            escapeshellarg($this->localUser),
            $this->localPass ? '-p' . escapeshellarg($this->localPass) : '',
            escapeshellarg($this->localDb),
            escapeshellarg($filename)
        );
        
        exec($command, $output, $return_var);
        
        if ($return_var !== 0) {
            throw new Exception('Errore durante il backup del database locale');
        }
        
        return $filename;
    }
    
    /**
     * Ripristina il backup sul database remoto
     */
    public function restoreToRemoteDatabase($backupFile) {
        // Verifica che il file esista
        if (!file_exists($backupFile)) {
            throw new Exception('File di backup non trovato');
        }
        
        // Comando per il ripristino
        $command = sprintf(
            'c:/xampp/mysql/bin/mysql -h %s -u %s %s %s < %s',
            escapeshellarg($this->remoteHost),
            escapeshellarg($this->remoteUser),
            $this->remotePass ? '-p' . escapeshellarg($this->remotePass) : '',
            escapeshellarg($this->remoteDb),
            escapeshellarg($backupFile)
        );
        
        exec($command, $output, $return_var);
        
        if ($return_var !== 0) {
            throw new Exception('Errore durante il ripristino del database remoto');
        }
    }
    
    /**
     * Esegue la sincronizzazione completa
     */
    public function sync() {
        try {
            echo "Inizio sincronizzazione database...\n";
            
            // Backup del database locale
            echo "Creazione backup del database locale...\n";
            $backupFile = $this->backupLocalDatabase();
            echo "Backup creato: " . basename($backupFile) . "\n";
            
            // Ripristino sul database remoto
            echo "Ripristino del database sul server remoto...\n";
            $this->restoreToRemoteDatabase($backupFile);
            echo "Ripristino completato con successo!\n";
            
            echo "Sincronizzazione completata con successo!\n";
            return true;
            
        } catch (Exception $e) {
            echo "Errore durante la sincronizzazione: " . $e->getMessage() . "\n";
            return false;
        }
    }
}

// Utilizzo dello script
if (php_sapi_name() === 'cli') {
    $syncer = new DatabaseSync();
    $syncer->sync();
} else {
    // Se in ambiente locale, permetti l'esecuzione
    if ($_SERVER['SERVER_NAME'] === 'localhost') {
        $syncer = new DatabaseSync();
        $result = $syncer->sync();
        
        if ($result) {
            echo json_encode(['success' => true, 'message' => 'Sincronizzazione completata con successo']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Errore durante la sincronizzazione']);
        }
    } else {
        // Se non in locale, verifica l'autenticazione
        session_start();
        if (!isset($_SESSION['user_id'])) {
            die('Accesso non autorizzato');
        }
        
        $syncer = new DatabaseSync();
        $result = $syncer->sync();
        
        if ($result) {
            echo json_encode(['success' => true, 'message' => 'Sincronizzazione completata con successo']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Errore durante la sincronizzazione']);
        }
    }
}
