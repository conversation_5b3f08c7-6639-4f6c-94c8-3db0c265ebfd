<?php

namespace App\Models;

use App\Config\Database;
use PDO;
use DateTime;

class Notifica {
    private $db;
    private $table = 'notifiche';

    public function __construct() {
        $this->db = Database::getInstance();
    }

    public function create($data) {
        $sql = "INSERT INTO {$this->table} (
            tipo, titolo, messaggio, data_creazione, data_scadenza, 
            stato, priorita, link
        ) VALUES (
            :tipo, :titolo, :messaggio, :data_creazione, :data_scadenza,
            :stato, :priorita, :link
        )";

        $stmt = $this->db->prepare($sql);
        if ($stmt->execute($data)) {
            return $this->db->lastInsertId();
        }
        return false;
    }

    public function assegnaUtenti($notificaId, $utenti) {
        $sql = "INSERT INTO notifiche_utenti (notifica_id, utente_id) VALUES (:notifica_id, :utente_id)";
        $stmt = $this->db->prepare($sql);

        foreach ($utenti as $utenteId) {
            $stmt->execute([
                'notifica_id' => $notificaId,
                'utente_id' => $utenteId
            ]);
        }
    }

    public function getNotificheUtente($utenteId, $stato = null, $limit = 10) {
        $sql = "SELECT n.*, nu.letta, nu.data_lettura 
                FROM {$this->table} n
                JOIN notifiche_utenti nu ON n.id = nu.notifica_id
                WHERE nu.utente_id = :utente_id";

        if ($stato) {
            $sql .= " AND n.stato = :stato";
        }

        $sql .= " ORDER BY n.data_creazione DESC LIMIT :limit";

        $stmt = $this->db->prepare($sql);
        $stmt->bindValue(':utente_id', $utenteId, PDO::PARAM_INT);
        if ($stato) {
            $stmt->bindValue(':stato', $stato, PDO::PARAM_STR);
        }
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function segnaComeLetta($notificaId, $utenteId) {
        $sql = "UPDATE notifiche_utenti 
                SET letta = TRUE, data_lettura = NOW() 
                WHERE notifica_id = :notifica_id AND utente_id = :utente_id";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            'notifica_id' => $notificaId,
            'utente_id' => $utenteId
        ]);
    }

    public function archivia($notificaId) {
        $sql = "UPDATE {$this->table} SET stato = 'archiviata' WHERE id = :id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute(['id' => $notificaId]);
    }

    public function getPreferenzeUtente($utenteId) {
        $sql = "SELECT * FROM preferenze_notifiche WHERE utente_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$utenteId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function updatePreferenze($utenteId, $preferenze) {
        $sql = "UPDATE preferenze_notifiche 
                SET email = :email, browser = :browser, anticipo_giorni = :anticipo_giorni
                WHERE utente_id = :utente_id AND tipo_notifica = :tipo_notifica";

        $stmt = $this->db->prepare($sql);
        
        foreach ($preferenze as $tipo => $pref) {
            $stmt->execute([
                'utente_id' => $utenteId,
                'tipo_notifica' => $tipo,
                'email' => $pref['email'] ? 1 : 0,
                'browser' => $pref['browser'] ? 1 : 0,
                'anticipo_giorni' => $pref['anticipo_giorni']
            ]);
        }
    }

    public function getScadenzeImminenti() {
        $sql = "SELECT p.id as pratica_id, p.numero_pratica, p.data_scadenza, 
                       p.data_scadenza_integrazione, pr.nome_progetto,
                       c.nome as cliente_nome, c.email as cliente_email,
                       u.id as utente_id, u.email as utente_email,
                       pn.anticipo_giorni, pn.email as notifica_email
                FROM pratiche p
                JOIN progetti pr ON p.progetto_id = pr.id
                JOIN clienti c ON pr.cliente_id = c.id
                JOIN utenti u ON u.id IN (SELECT utente_id FROM pratiche_utenti WHERE pratica_id = p.id)
                JOIN preferenze_notifiche pn ON u.id = pn.utente_id
                WHERE (
                    (p.data_scadenza IS NOT NULL AND 
                     p.data_scadenza >= CURDATE() AND 
                     p.data_scadenza <= DATE_ADD(CURDATE(), INTERVAL pn.anticipo_giorni DAY))
                    OR
                    (p.data_scadenza_integrazione IS NOT NULL AND 
                     p.data_scadenza_integrazione >= CURDATE() AND 
                     p.data_scadenza_integrazione <= DATE_ADD(CURDATE(), INTERVAL pn.anticipo_giorni DAY))
                )
                AND p.stato NOT IN ('completata', 'archiviata')
                AND pn.tipo_notifica IN ('scadenza_pratica', 'scadenza_integrazione')";

        return $this->db->query($sql)->fetchAll(PDO::FETCH_ASSOC);
    }

    public function creaNotificaScadenza($pratica, $tipo, $dataScadenza) {
        $giorni = (new DateTime($dataScadenza))->diff(new DateTime())->days;
        
        $data = [
            'tipo' => $tipo,
            'titolo' => "Scadenza {$tipo} - {$pratica['numero_pratica']}",
            'messaggio' => "La pratica {$pratica['numero_pratica']} ({$pratica['nome_progetto']}) " .
                          "scadrà tra {$giorni} giorni, il " . date('d/m/Y', strtotime($dataScadenza)),
            'data_creazione' => date('Y-m-d H:i:s'),
            'data_scadenza' => $dataScadenza,
            'stato' => 'non_letta',
            'priorita' => $giorni <= 3 ? 'alta' : ($giorni <= 7 ? 'media' : 'bassa'),
            'link' => "pratiche/modifica/{$pratica['pratica_id']}"
        ];

        $notificaId = $this->create($data);
        if ($notificaId) {
            $this->assegnaUtenti($notificaId, [$pratica['utente_id']]);
            
            if ($pratica['notifica_email']) {
                // Invia email (implementare separatamente)
                $this->inviaEmailNotifica($pratica['utente_email'], $data);
            }
        }
    }

    private function inviaEmailNotifica($email, $data) {
        // Implementare l'invio email usando una classe dedicata
        // Per ora solo log
        error_log("Email notifica da inviare a {$email}: {$data['titolo']}");
    }
}
